// Script de prueba para el endpoint createDeliveryBooking
// Ejecutar con: node test-endpoint-delivery.js

const https = require('https');

// Configuración del endpoint
const ENDPOINT_URL = 'https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBooking';

// Datos de prueba mejorados basados en tu estructura actual
const testData = {
  "folioEntrega": "ENT-TEST-" + Date.now(),
  "origen": {
    "direccion": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jal.",
    "lat": 20.6858704,
    "lng": -103.3154975,
    "referencias": "Almacén principal - Edificio corporativo"
  },
  "pedidos": [
    {
      "folioPedido": "PED-TEST-001",
      "cliente": {
        "uid": "VeSkwck41kXpdJIoDQH4sMjYror1",
        "nombre": "<PERSON>",
        "telefono": "+************",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "C. Álvarez del Castillo 760, Blanco y Cuéllar, 44370 Guadalajara, Jal.",
        "referencias": "Edificio blanco con portón café, timbre 3A"
      },
      "productos": [
        {
          "id": "PROD-TEST-001",
          "nombre": "Laptop Dell Inspiron (PRUEBA)",
          "cantidad": 1,
          "precio": 15000.00,
          "peso": 2.5,
          "dimensiones": "35x25x2 cm"
        },
        {
          "id": "PROD-TEST-002",
          "nombre": "Mouse inalámbrico (PRUEBA)",
          "cantidad": 1,
          "precio": 500.00,
          "peso": 0.1,
          "dimensiones": "10x6x3 cm"
        }
      ],
      "total": 15500.00,
      "instrucciones": "Entregar en recepción del edificio - MODO PRUEBA",
      "observaciones": "Cliente de prueba - disponible 24/7",
      "prioridad": "ALTA",
      "ventanaEntrega": {
        "inicio": "09:00",
        "fin": "18:00"
      },
      "tipoEntrega": "DOMICILIO",
      "requiereEvidencia": true,
      "requiereFirma": true
    },
    {
      "folioPedido": "PED-TEST-002",
      "cliente": {
        "uid": "saWRbrfvJRawpH3ccXCvAhdCbAu2",
        "nombre": "María González",
        "telefono": "+52555987654",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "C. Industria 1512, San Juan Bosco, 44730 Guadalajara, Jal.",
        "referencias": "Local comercial con letrero azul"
      },
      "productos": [
        {
          "id": "PROD-TEST-003",
          "nombre": "Impresora HP LaserJet (PRUEBA)",
          "cantidad": 1,
          "precio": 3500.00,
          "peso": 8.5,
          "dimensiones": "40x30x25 cm"
        },
        {
          "id": "PROD-TEST-004",
          "nombre": "Papel A4 paquete (PRUEBA)",
          "cantidad": 5,
          "precio": 150.00,
          "peso": 2.5,
          "dimensiones": "30x21x5 cm"
        }
      ],
      "total": 4250.00,
      "instrucciones": "Tocar timbre y preguntar por María - MODO PRUEBA",
      "observaciones": "Negocio de prueba - abierto 24/7",
      "prioridad": "MEDIA",
      "ventanaEntrega": {
        "inicio": "10:00",
        "fin": "16:00"
      },
      "tipoEntrega": "COMERCIAL",
      "requiereEvidencia": true,
      "requiereFirma": false
    }
  ],
  "vehiculo": {
    "id": "-OVUvzOLck6hU7Ppgfkb",
    "tipo": "CAMIONETA",
    "placas": "Eribdc34",
    "modelo": "Ram 2500",
    "marca": "Ford",
    "capacidad": "1500kg",
    "capacidadVolumen": "10m³",
    "combustible": "GASOLINA",
    "estado": "DISPONIBLE"
  },
  "chofer": {
    "uid": "test_driver_001",
    "nombre": "Diego Ballesteros",
    "telefono": "+************",
    "pushToken": "test_firebase_token",
    "licencia": "ABC123456",
    "experiencia": "5 años",
    "calificacion": 4.8
  },
  "configuracionEntrega": {
    "optimizarRuta": true,
    "permitirReordenamiento": true,
    "notificarClientes": true,
    "requiereConfirmacion": false,
    "tiempoMaximoEntrega": 480,
    "distanciaMaxima": 50
  },
  "metadatos": {
    "creadoPor": "sistema_pruebas",
    "canal": "WEB_ADMIN",
    "version": "2.0",
    "entorno": "TEST"
  },
  "observacionesGenerales": "🧪 ENTREGA DE PRUEBA - Ruta optimizada para zona centro. Este es un booking de prueba sin autenticación."
};

// Función para hacer la petición HTTP
function testEndpoint() {
  const postData = JSON.stringify(testData);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      // Nota: En producción necesitarás agregar el header de autorización
      // 'Authorization': 'Basic ' + Buffer.from('username:password').toString('base64')
    }
  };

  console.log('🚀 Iniciando prueba del endpoint createDeliveryBooking...');
  console.log('📍 URL:', ENDPOINT_URL);
  console.log('📦 Datos de prueba:', JSON.stringify(testData, null, 2));
  console.log('\n⏳ Enviando petición...\n');

  const req = https.request(ENDPOINT_URL, options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📊 Código de respuesta:', res.statusCode);
      console.log('📋 Headers de respuesta:', res.headers);
      
      try {
        const response = JSON.parse(data);
        console.log('\n✅ Respuesta del servidor:');
        console.log(JSON.stringify(response, null, 2));
        
        if (res.statusCode === 200 && response.success) {
          console.log('\n🎉 ¡Prueba exitosa!');
          console.log('📝 Folio de entrega creado:', response.folioEntrega);
          console.log('📊 Total de pedidos:', response.totalPedidos);
          console.log('🚛 Conductor asignado:', response.choferAsignado.nombre);
          console.log('🚗 Vehículo asignado:', response.vehiculoAsignado.placas);
        } else {
          console.log('\n❌ Error en la prueba');
          console.log('💬 Mensaje:', response.error || response.message);
        }
      } catch (error) {
        console.log('\n❌ Error al parsear la respuesta JSON:');
        console.log('📄 Respuesta cruda:', data);
        console.log('🐛 Error:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ Error en la petición:');
    console.log('🐛 Error:', error.message);
  });

  req.write(postData);
  req.end();
}

// Función para mostrar información de uso
function showUsage() {
  console.log(`
📚 GUÍA DE USO DEL ENDPOINT createDeliveryBooking

🔗 URL del Endpoint:
${ENDPOINT_URL}

🔑 Autenticación Requerida:
- Header: Authorization: Basic [base64(username:password)]
- Contacta al administrador para obtener las credenciales

📋 Estructura de Datos Requerida:
{
  "folioEntrega": "Identificador único de la entrega",
  "pedidos": [
    {
      "folioPedido": "Folio del pedido individual",
      "cliente": {
        "uid": "ID del cliente",
        "nombre": "Nombre completo",
        "telefono": "Teléfono con código de país",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Dirección completa",
        "lat": 19.4326,
        "lng": -99.1332,
        "referencias": "Referencias adicionales"
      },
      "productos": [
        {
          "id": "ID del producto",
          "nombre": "Nombre del producto",
          "cantidad": 1,
          "precio": 100.00
        }
      ],
      "total": 100.00,
      "instrucciones": "Instrucciones de entrega",
      "observaciones": "Observaciones adicionales"
    }
  ],
  "vehiculo": {
    "id": "ID del vehículo",
    "tipo": "Tipo de vehículo",
    "placas": "Placas del vehículo",
    "modelo": "Modelo",
    "marca": "Marca"
  },
  "chofer": {
    "uid": "ID del conductor",
    "nombre": "Nombre del conductor",
    "telefono": "Teléfono del conductor",
    "pushToken": "Token para notificaciones"
  },
  "observacionesGenerales": "Observaciones generales de la entrega"
}

🎯 Estados del Sistema:
- Booking: NEW → ACCEPTED → REACHED → PENDING → PAID → COMPLETE
- Grupo de Entrega: PENDING → ASSIGNED → IN_PROGRESS → COMPLETED
- Pedido Individual: PENDING → DELIVERED

📱 Notificaciones:
- Se envía notificación push al conductor cuando se asigna la entrega
- Se actualiza el estado del conductor (queue: true)

🗄️ Base de Datos:
- /bookings/{bookingId} - Bookings individuales
- /deliveryGroups/{folioEntrega} - Grupos de entrega
- /users/{uid} - Información de usuarios

⚠️  Nota: Esta es una prueba de ejemplo. En producción necesitarás:
1. Credenciales de autenticación válidas
2. Conductores y vehículos reales en la base de datos
3. Configuración correcta de Firebase
  `);
}

// Ejecutar la prueba
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
} else {
  testEndpoint();
}
