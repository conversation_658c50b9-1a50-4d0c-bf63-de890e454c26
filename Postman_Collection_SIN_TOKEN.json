{"info": {"_postman_id": "test-no-auth-googlemaps-12345", "name": "Entregas Agrupadas - SIN TOKEN + GOOGLE MAPS", "description": "Colección para probar el endpoint de entregas agrupadas SIN autenticación e integración completa con Google Maps para geocodificación y optimización de rutas", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Crear Entrega Agrupada (SIN TOKEN)", "event": [{"listen": "prerequest", "script": {"exec": ["// Generar folio único para la entrega", "const timestamp = Date.now();", "const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');", "const folioEntrega = `ENT-TEST-${timestamp}-${random}`;", "pm.environment.set('folioEntrega', folioEntrega);", "", "// Generar folios únicos para pedidos", "pm.environment.set('folioPedido1', `PED-TEST-${timestamp}-001`);", "pm.environment.set('folioPedido2', `PED-TEST-${timestamp}-002`);", "", "console.log('🧪 Folio de Entrega de PRUEBA generado:', folioEntrega);", "console.log('⚠️ Este endpoint NO requiere autenticación');"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Verificar que la respuesta sea exitosa", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Verificar estructura de respuesta", "pm.test('Response has required fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('folioEntrega');", "    pm.expect(jsonData).to.have.property('totalPedidos');", "    pm.expect(jsonData).to.have.property('testMode');", "});", "", "// Verificar que es modo de prueba", "pm.test('Response is in test mode', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.testMode).to.be.true;", "});", "", "// Guardar folio para siguientes requests", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    pm.environment.set('lastFolioEntrega', jsonData.folioEntrega);", "    console.log('✅ Entrega de PRUEBA creada exitosamente:', jsonData.folioEntrega);", "    console.log('🧪 Modo de prueba activo - sin autenticación');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"folioEntrega\": \"{{folioEntrega}}\",\n  \"origen\": {\n    \"direccion\": \"Almacén Central, Av. Insurgentes Sur 1602, Ciudad de México, CDMX\",\n    \"lat\": 19.3910,\n    \"lng\": -99.1620\n  },\n  \"pedidos\": [\n    {\n      \"folioPedido\": \"{{folioPedido1}}\",\n      \"cliente\": {\n        \"uid\": \"test_customer_001\",\n        \"nombre\": \"<PERSON> (PRUEBA)\",\n        \"telefono\": \"+52555123456\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"domicilio\": {\n        \"direccion\": \"Av. <PERSON><PERSON><PERSON>, Roma Norte, Cuauhtémoc, 06700 Ciudad de México, CDMX\",\n        \"referencias\": \"Edificio blanco con portón café, timbre 3A (PRUEBA) - Google Maps geocodificará automáticamente\"\n      },\n      \"productos\": [\n        {\n          \"id\": \"PROD-TEST-001\",\n          \"nombre\": \"Laptop Dell Inspiron (PRUEBA)\",\n          \"cantidad\": 1,\n          \"precio\": 15000.00\n        },\n        {\n          \"id\": \"PROD-TEST-002\",\n          \"nombre\": \"Mouse inalámbrico (PRUEBA)\",\n          \"cantidad\": 1,\n          \"precio\": 500.00\n        }\n      ],\n      \"total\": 15500.00,\n      \"instrucciones\": \"Entregar en recepción del edificio - MODO PRUEBA\",\n      \"observaciones\": \"Cliente de prueba - disponible 24/7\"\n    },\n    {\n      \"folioPedido\": \"{{folioPedido2}}\",\n      \"cliente\": {\n        \"uid\": \"test_customer_002\",\n        \"nombre\": \"María González (PRUEBA)\",\n        \"telefono\": \"+52555987654\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"domicilio\": {\n        \"direccion\": \"Calle Francisco I. Madero 17, Centro Histórico de la Cdad. de México, Centro, Cuauhtémoc, 06000 Ciudad de México, CDMX\",\n        \"referencias\": \"Local comercial con letrero azul (PRUEBA) - Google Maps geocodificará automáticamente\"\n      },\n      \"productos\": [\n        {\n          \"id\": \"PROD-TEST-003\",\n          \"nombre\": \"Impresora HP LaserJet (PRUEBA)\",\n          \"cantidad\": 1,\n          \"precio\": 3500.00\n        },\n        {\n          \"id\": \"PROD-TEST-004\",\n          \"nombre\": \"Papel A4 paquete (PRUEBA)\",\n          \"cantidad\": 5,\n          \"precio\": 150.00\n        }\n      ],\n      \"total\": 4250.00,\n      \"instrucciones\": \"Tocar timbre y preguntar por María - MODO PRUEBA\",\n      \"observaciones\": \"Negocio de prueba - abierto 24/7\"\n    }\n  ],\n  \"vehiculo\": {\n    \"id\": \"VEH-TEST-001\",\n    \"tipo\": \"Camioneta de Prueba\",\n    \"placas\": \"TEST-123\",\n    \"modelo\": \"Ford Transit (PRUEBA)\",\n    \"marca\": \"Ford\",\n    \"capacidad\": \"1500kg\"\n  },\n  \"chofer\": {\n    \"uid\": \"test_driver_001\",\n    \"nombre\": \"Carlos López (CONDUCTOR PRUEBA)\",\n    \"telefono\": \"+52555777888\",\n    \"pushToken\": \"test_firebase_token_carlos_123\"\n  },\n  \"observacionesGenerales\": \"🧪 ENTREGA DE PRUEBA - Ruta optimizada para zona centro. Este es un booking de prueba sin autenticación.\"\n}"}, "url": {"raw": "{{base_url_test}}/createDeliveryBookingTest", "host": ["{{base_url_test}}"], "path": ["createDeliveryBookingTest"]}, "description": "🧪 Endpoint de PRUEBA con integración completa de Google Maps. SIN autenticación requerida. Incluye: geocodificación automática de direcciones, optimización de rutas, cálculo de distancias y tiempos reales. Perfecto para testing y desarrollo."}, "response": [{"name": "Respuesta <PERSON>osa (Modo Prueba)", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"folioEntrega\": \"ENT-TEST-1642694400000-001\",\n  \"pedidos\": [...],\n  \"vehiculo\": {...},\n  \"chofer\": {...}\n}"}, "url": {"raw": "{{base_url_test}}/createDeliveryBookingTest", "host": ["{{base_url_test}}"], "path": ["createDeliveryBookingTest"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Entrega agrupada creada exitosamente (modo prueba)\",\n  \"folioEntrega\": \"ENT-TEST-1642694400000-001\",\n  \"totalPedidos\": 2,\n  \"bookingsCreados\": [\n    {\n      \"bookingId\": \"booking_test_123\",\n      \"folioPedido\": \"PED-TEST-1642694400000-001\",\n      \"reference\": \"ABCDEF\"\n    },\n    {\n      \"bookingId\": \"booking_test_124\",\n      \"folioPedido\": \"PED-TEST-1642694400000-002\",\n      \"reference\": \"GHIJKL\"\n    }\n  ],\n  \"choferAsignado\": {\n    \"uid\": \"test_driver_001\",\n    \"nombre\": \"<PERSON> (CONDUCTOR PRUEBA)\"\n  },\n  \"vehiculoAsignado\": {\n    \"id\": \"VEH-TEST-001\",\n    \"tipo\": \"Camioneta de Prueba\",\n    \"placas\": \"TEST-123\"\n  },\n  \"testMode\": true,\n  \"note\": \"Este endpoint es solo para pruebas y no requiere autenticación\"\n}"}]}, {"name": "Entrega Simple (SIN TOKEN)", "event": [{"listen": "prerequest", "script": {"exec": ["// Generar folio único para entrega simple", "const timestamp = Date.now();", "const folioSimple = `ENT-SIMPLE-TEST-${timestamp}`;", "pm.environment.set('folioSimple', folioSimple);", "pm.environment.set('pedidoSimple', `PED-SIMPLE-TEST-${timestamp}`);", "", "console.log('🧪 Entrega simple de PRUEBA:', folioSimple);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"folioEntrega\": \"{{folioSimple}}\",\n  \"pedidos\": [\n    {\n      \"folioPedido\": \"{{pedidoSimple}}\",\n      \"cliente\": {\n        \"uid\": \"test_customer_simple\",\n        \"nombre\": \"<PERSON> (PRUEBA SIMPLE)\",\n        \"telefono\": \"+52555111222\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"domicilio\": {\n        \"direccion\": \"Av. Reforma 789, Col. <PERSON>, CDMX\",\n        \"lat\": 19.4260,\n        \"lng\": -99.1700,\n        \"referencias\": \"Torre de oficinas, piso 15 (PRUEBA)\"\n      },\n      \"productos\": [\n        {\n          \"id\": \"PROD-SIMPLE-TEST\",\n          \"nombre\": \"Documento importante (PRUEBA)\",\n          \"cantidad\": 1,\n          \"precio\": 0.00\n        }\n      ],\n      \"total\": 50.00,\n      \"instrucciones\": \"Entregar en recepción, mencionar empresa TechCorp - MODO PRUEBA\",\n      \"observaciones\": \"🧪 Entrega urgente de prueba - disponible 24/7\"\n    }\n  ],\n  \"vehiculo\": {\n    \"id\": \"VEH-TEST-002\",\n    \"tipo\": \"Motocicleta de Prueba\",\n    \"placas\": \"MOTO-TEST\",\n    \"modelo\": \"Honda CBR (PRUEBA)\",\n    \"marca\": \"Honda\"\n  },\n  \"chofer\": {\n    \"uid\": \"test_driver_002\",\n    \"nombre\": \"Roberto Martínez (CONDUCTOR PRUEBA)\",\n    \"telefono\": \"+52555333444\",\n    \"pushToken\": \"test_firebase_token_roberto_456\"\n  },\n  \"observacionesGenerales\": \"🧪 ENTREGA EXPRESS DE PRUEBA - documento urgente sin autenticación\"\n}"}, "url": {"raw": "{{base_url_test}}/createDeliveryBookingTest", "host": ["{{base_url_test}}"], "path": ["createDeliveryBookingTest"]}, "description": "🧪 Ejemplo de entrega simple de PRUEBA con un solo pedido, sin necesidad de autenticación."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script global para modo de prueba", "console.log('🧪 MODO PRUEBA ACTIVO - Sin autenticación requerida');", "console.log('📍 <PERSON><PERSON><PERSON><PERSON><PERSON> request a:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script global de validación", "console.log('📊 Response status:', pm.response.status);", "console.log('⏱️ Response time:', pm.response.responseTime + 'ms');", "", "// Verificar que es modo de prueba", "if (pm.response.code === 200) {", "    try {", "        const jsonData = pm.response.json();", "        if (jsonData.testMode) {", "            console.log('✅ Confirmado: Modo de prueba activo');", "        }", "    } catch (e) {", "        console.log('⚠️ No se pudo verificar modo de prueba');", "    }", "}"]}}], "variable": [{"key": "base_url_test", "value": "https://us-central1-balle-813e3.cloudfunctions.net", "type": "string", "description": "URL base para endpoints de prueba sin autenticación"}]}