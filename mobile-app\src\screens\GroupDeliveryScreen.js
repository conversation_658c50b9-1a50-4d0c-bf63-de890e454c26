import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Alert,
    Modal,
    TextInput,
    Image,
    ScrollView,
    Dimensions,
    Platform
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { <PERSON><PERSON>, Header } from 'react-native-elements';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { colors } from '../common/theme';
import { api } from 'common';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';

const { width, height } = Dimensions.get('window');

export default function GroupDeliveryScreen(props) {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const { folioEntrega } = props.route.params;
    
    const [deliveryGroup, setDeliveryGroup] = useState(null);
    const [currentLocation, setCurrentLocation] = useState(null);
    const [selectedPedido, setSelectedPedido] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [deliveryModalVisible, setDeliveryModalVisible] = useState(false);
    const [observaciones, setObservaciones] = useState('');
    const [recibidoPor, setRecibidoPor] = useState('');
    const [evidenciaFoto, setEvidenciaFoto] = useState(null);
    const [loading, setLoading] = useState(false);
    
    const mapRef = useRef(null);
    const auth = useSelector(state => state.auth);
    const gpsdata = useSelector(state => state.gpsdata);

    useEffect(() => {
        loadDeliveryDetails();
        getCurrentLocation();
    }, [folioEntrega]);

    useEffect(() => {
        if (gpsdata.location) {
            setCurrentLocation(gpsdata.location);
        }
    }, [gpsdata.location]);

    const loadDeliveryDetails = async () => {
        try {
            setLoading(true);
            const delivery = await api.getGroupDeliveryDetails(folioEntrega);
            setDeliveryGroup(delivery);
        } catch (error) {
            Alert.alert(t('error'), error.message);
        } finally {
            setLoading(false);
        }
    };

    const getCurrentLocation = async () => {
        try {
            const { status } = await Location.requestForegroundPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert(t('error'), t('location_permission_denied'));
                return;
            }

            const location = await Location.getCurrentPositionAsync({});
            setCurrentLocation({
                lat: location.coords.latitude,
                lng: location.coords.longitude
            });
        } catch (error) {
            console.error('Error getting location:', error);
        }
    };

    const startDeliveryRoute = async () => {
        try {
            await api.updateGroupDeliveryStatus(folioEntrega, 'IN_PROGRESS', '');
            Alert.alert(t('success'), t('delivery_route_started'));
            loadDeliveryDetails();
        } catch (error) {
            Alert.alert(t('error'), error.message);
        }
    };

    const openPedidoDetails = (pedido) => {
        setSelectedPedido(pedido);
        setModalVisible(true);
    };

    const openDeliveryModal = (pedido) => {
        setSelectedPedido(pedido);
        setObservaciones('');
        setRecibidoPor('');
        setEvidenciaFoto(null);
        setDeliveryModalVisible(true);
    };

    const takePhoto = async () => {
        try {
            const { status } = await ImagePicker.requestCameraPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert(t('error'), t('camera_permission_denied'));
                return;
            }

            const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled) {
                setEvidenciaFoto(result.assets[0].uri);
            }
        } catch (error) {
            Alert.alert(t('error'), t('error_taking_photo'));
        }
    };

    const markPedidoAsDelivered = async (status) => {
        if (!selectedPedido) return;

        try {
            setLoading(true);
            
            const evidencia = {
                foto: evidenciaFoto || '',
                recibidoPor: recibidoPor
            };

            await api.updatePedidoDeliveryStatus(
                folioEntrega,
                selectedPedido.folioPedido,
                status,
                evidencia,
                observaciones
            );

            Alert.alert(t('success'), t('pedido_status_updated'));
            setDeliveryModalVisible(false);
            loadDeliveryDetails();
        } catch (error) {
            Alert.alert(t('error'), error.message);
        } finally {
            setLoading(false);
        }
    };

    const completeAllDeliveries = async () => {
        try {
            await api.updateGroupDeliveryStatus(folioEntrega, 'COMPLETED', '');
            Alert.alert(t('success'), t('all_deliveries_completed'), [
                { text: t('ok'), onPress: () => props.navigation.goBack() }
            ]);
        } catch (error) {
            Alert.alert(t('error'), error.message);
        }
    };

    const renderPedidoItem = ({ item }) => {
        const statusColor = item.statusEntrega === 'DELIVERED' ? colors.GREEN : 
                           item.statusEntrega === 'PARTIAL' ? colors.YELLOW : colors.RED;
        
        return (
            <TouchableOpacity 
                style={styles.pedidoItem}
                onPress={() => openPedidoDetails(item)}
            >
                <View style={styles.pedidoHeader}>
                    <Text style={styles.pedidoId}>{item.folioPedido}</Text>
                    <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
                        <Text style={styles.statusText}>{t(item.statusEntrega)}</Text>
                    </View>
                </View>
                
                <Text style={styles.clienteName}>{item.cliente.nombre}</Text>
                <Text style={styles.direccion}>{item.domicilio.direccion}</Text>
                
                <View style={styles.pedidoActions}>
                    <TouchableOpacity 
                        style={styles.actionButton}
                        onPress={() => openDeliveryModal(item)}
                        disabled={item.statusEntrega === 'DELIVERED'}
                    >
                        <MaterialIcons 
                            name="delivery-dining" 
                            size={20} 
                            color={item.statusEntrega === 'DELIVERED' ? colors.GRAY : colors.WHITE} 
                        />
                        <Text style={[styles.actionButtonText, 
                            { color: item.statusEntrega === 'DELIVERED' ? colors.GRAY : colors.WHITE }]}>
                            {t('mark_delivered')}
                        </Text>
                    </TouchableOpacity>
                </View>
            </TouchableOpacity>
        );
    };

    const renderMap = () => {
        if (!deliveryGroup || !currentLocation) return null;

        const markers = deliveryGroup.ruta.puntos.map((punto, index) => (
            <Marker
                key={punto.pedidoId}
                coordinate={{
                    latitude: punto.lat,
                    longitude: punto.lng
                }}
                title={`Parada ${punto.orden}`}
                description={punto.direccion}
                pinColor={deliveryGroup.pedidos[punto.pedidoId]?.statusEntrega === 'DELIVERED' ? colors.GREEN : colors.RED}
            />
        ));

        return (
            <MapView
                ref={mapRef}
                style={styles.map}
                provider={PROVIDER_GOOGLE}
                initialRegion={{
                    latitude: currentLocation.lat,
                    longitude: currentLocation.lng,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                }}
                showsUserLocation={true}
                showsMyLocationButton={true}
            >
                {markers}
                
                {currentLocation && (
                    <Marker
                        coordinate={{
                            latitude: currentLocation.lat,
                            longitude: currentLocation.lng
                        }}
                        title={t('your_location')}
                        pinColor={colors.BLUE}
                    />
                )}
            </MapView>
        );
    };

    if (loading && !deliveryGroup) {
        return (
            <View style={styles.loadingContainer}>
                <Text>{t('loading')}</Text>
            </View>
        );
    }

    if (!deliveryGroup) {
        return (
            <View style={styles.errorContainer}>
                <Text>{t('delivery_not_found')}</Text>
                <Button title={t('go_back')} onPress={() => props.navigation.goBack()} />
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Header
                centerComponent={{
                    text: `${t('delivery')} ${folioEntrega}`,
                    style: { color: colors.WHITE, fontSize: 18, fontWeight: 'bold' }
                }}
                backgroundColor={colors.HEADER}
                leftComponent={{
                    icon: 'arrow-back',
                    color: colors.WHITE,
                    onPress: () => props.navigation.goBack()
                }}
            />

            <View style={styles.deliveryInfo}>
                <Text style={styles.deliveryTitle}>{t('delivery_group_info')}</Text>
                <Text>{t('total_orders')}: {deliveryGroup.totalPedidos}</Text>
                <Text>{t('delivered_orders')}: {deliveryGroup.pedidosEntregados}</Text>
                <Text>{t('status')}: {t(deliveryGroup.status)}</Text>
            </View>

            <View style={styles.mapContainer}>
                {renderMap()}
            </View>

            <View style={styles.actionsContainer}>
                {deliveryGroup.status === 'PENDING' && (
                    <Button
                        title={t('start_delivery_route')}
                        onPress={startDeliveryRoute}
                        buttonStyle={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                    />
                )}
                
                {deliveryGroup.status === 'IN_PROGRESS' && deliveryGroup.pedidosEntregados === deliveryGroup.totalPedidos && (
                    <Button
                        title={t('complete_all_deliveries')}
                        onPress={completeAllDeliveries}
                        buttonStyle={[styles.actionButton, { backgroundColor: colors.BLUE }]}
                    />
                )}
            </View>

            <FlatList
                data={Object.values(deliveryGroup.pedidos)}
                renderItem={renderPedidoItem}
                keyExtractor={(item) => item.folioPedido}
                style={styles.pedidosList}
            />

            {/* Modal de detalles del pedido */}
            <Modal
                visible={modalVisible}
                animationType="slide"
                transparent={true}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        {selectedPedido && (
                            <ScrollView>
                                <Text style={styles.modalTitle}>{t('order_details')}</Text>
                                <Text style={styles.modalText}>{t('order_id')}: {selectedPedido.folioPedido}</Text>
                                <Text style={styles.modalText}>{t('customer')}: {selectedPedido.cliente.nombre}</Text>
                                <Text style={styles.modalText}>{t('phone')}: {selectedPedido.cliente.telefono}</Text>
                                <Text style={styles.modalText}>{t('address')}: {selectedPedido.domicilio.direccion}</Text>
                                
                                <Text style={styles.modalSubtitle}>{t('products')}:</Text>
                                {selectedPedido.productos?.map((producto, index) => (
                                    <Text key={index} style={styles.modalText}>
                                        • {producto.nombre} x{producto.cantidad} - ${producto.precio}
                                    </Text>
                                ))}
                                
                                <View style={styles.modalActions}>
                                    <Button
                                        title={t('close')}
                                        onPress={() => setModalVisible(false)}
                                        buttonStyle={styles.modalButton}
                                    />
                                </View>
                            </ScrollView>
                        )}
                    </View>
                </View>
            </Modal>

            {/* Modal de entrega */}
            <Modal
                visible={deliveryModalVisible}
                animationType="slide"
                transparent={true}
                onRequestClose={() => setDeliveryModalVisible(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>{t('mark_delivery')}</Text>
                        
                        <TextInput
                            style={styles.textInput}
                            placeholder={t('received_by')}
                            value={recibidoPor}
                            onChangeText={setRecibidoPor}
                        />
                        
                        <TextInput
                            style={[styles.textInput, styles.textArea]}
                            placeholder={t('observations')}
                            value={observaciones}
                            onChangeText={setObservaciones}
                            multiline={true}
                            numberOfLines={3}
                        />
                        
                        <TouchableOpacity style={styles.photoButton} onPress={takePhoto}>
                            <MaterialIcons name="camera-alt" size={24} color={colors.WHITE} />
                            <Text style={styles.photoButtonText}>{t('take_photo')}</Text>
                        </TouchableOpacity>
                        
                        {evidenciaFoto && (
                            <Image source={{ uri: evidenciaFoto }} style={styles.evidenceImage} />
                        )}
                        
                        <View style={styles.modalActions}>
                            <Button
                                title={t('delivered')}
                                onPress={() => markPedidoAsDelivered('DELIVERED')}
                                buttonStyle={[styles.modalButton, { backgroundColor: colors.GREEN }]}
                                loading={loading}
                            />
                            <Button
                                title={t('partial')}
                                onPress={() => markPedidoAsDelivered('PARTIAL')}
                                buttonStyle={[styles.modalButton, { backgroundColor: colors.YELLOW }]}
                                loading={loading}
                            />
                            <Button
                                title={t('not_delivered')}
                                onPress={() => markPedidoAsDelivered('NOT_DELIVERED')}
                                buttonStyle={[styles.modalButton, { backgroundColor: colors.RED }]}
                                loading={loading}
                            />
                            <Button
                                title={t('cancel')}
                                onPress={() => setDeliveryModalVisible(false)}
                                buttonStyle={styles.modalButton}
                            />
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    deliveryInfo: {
        padding: 15,
        backgroundColor: colors.LIGHT_GRAY,
        borderBottomWidth: 1,
        borderBottomColor: colors.BORDER_BACKGROUND,
    },
    deliveryTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 5,
    },
    mapContainer: {
        height: height * 0.3,
    },
    map: {
        flex: 1,
    },
    actionsContainer: {
        padding: 10,
    },
    actionButton: {
        marginVertical: 5,
    },
    pedidosList: {
        flex: 1,
    },
    pedidoItem: {
        backgroundColor: colors.WHITE,
        margin: 10,
        padding: 15,
        borderRadius: 8,
        elevation: 2,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    pedidoHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
    },
    pedidoId: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        color: colors.WHITE,
        fontSize: 12,
        fontWeight: 'bold',
    },
    clienteName: {
        fontSize: 14,
        fontWeight: '600',
        marginBottom: 5,
    },
    direccion: {
        fontSize: 12,
        color: colors.GRAY,
        marginBottom: 10,
    },
    pedidoActions: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    actionButtonText: {
        marginLeft: 5,
        fontSize: 12,
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        backgroundColor: colors.WHITE,
        padding: 20,
        borderRadius: 10,
        width: width * 0.9,
        maxHeight: height * 0.8,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
    },
    modalSubtitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginTop: 10,
        marginBottom: 5,
    },
    modalText: {
        fontSize: 14,
        marginBottom: 5,
    },
    modalActions: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-around',
        marginTop: 20,
    },
    modalButton: {
        minWidth: 80,
        margin: 5,
    },
    textInput: {
        borderWidth: 1,
        borderColor: colors.BORDER_BACKGROUND,
        borderRadius: 5,
        padding: 10,
        marginBottom: 10,
        fontSize: 14,
    },
    textArea: {
        height: 80,
        textAlignVertical: 'top',
    },
    photoButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.BLUE,
        padding: 10,
        borderRadius: 5,
        marginBottom: 10,
    },
    photoButtonText: {
        color: colors.WHITE,
        marginLeft: 5,
        fontWeight: 'bold',
    },
    evidenceImage: {
        width: 100,
        height: 100,
        borderRadius: 5,
        marginBottom: 10,
        alignSelf: 'center',
    },
});
