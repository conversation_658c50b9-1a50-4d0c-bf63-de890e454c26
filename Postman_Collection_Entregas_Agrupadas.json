{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "Sistema de Entregas Agrupadas", "description": "Colección de endpoints para el sistema de entregas agrupadas con múltiples pedidos por conductor", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON><PERSON> En<PERSON>ga Agrupada", "event": [{"listen": "prerequest", "script": {"exec": ["// Generar folio único para la entrega", "const timestamp = Date.now();", "const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');", "const folioEntrega = `ENT-${timestamp}-${random}`;", "pm.environment.set('folioEntrega', folioEntrega);", "", "// Generar folios únicos para pedidos", "const folioPedido1 = `PED-${timestamp}-001`;", "const folioPedido2 = `PED-${timestamp}-002`;", "pm.environment.set('folioPedido1', folioPedido1);", "pm.environment.set('folioPedido2', folioPedido2);", "", "console.log('Folio de Entrega generado:', folioEntrega);", "console.log('Folios de Pedidos:', folioPedido1, folioPedido2);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Verificar que la respuesta sea exitosa", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Verificar estructura de respuesta", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});", "", "// Verificar que se creó el folio de entrega", "pm.test('Response has folioEntrega', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('folioEntrega');", "    pm.environment.set('lastFolioEntrega', jsonData.folioEntrega);", "});", "", "// Verificar que se crearon los bookings", "pm.test('Response has bookingsCreados', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('bookingsCreados');", "    pm.expect(jsonData.bookingsCreados).to.be.an('array');", "    pm.expect(jsonData.bookingsCreados.length).to.be.greaterThan(0);", "});", "", "// Guardar información para siguientes requests", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    pm.environment.set('lastFolioEntrega', jsonData.folioEntrega);", "    if (jsonData.bookingsCreados && jsonData.bookingsCreados.length > 0) {", "        pm.environment.set('lastBookingId', jsonData.bookingsCreados[0].bookingId);", "    }", "    console.log('✅ Entrega creada exitosamente:', jsonData.folioEntrega);", "} else {", "    console.log('❌ Error en la creación de entrega');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Basic {{auth_token}}", "type": "text", "description": "Reemplazar {{auth_token}} con base64(username:password)"}], "body": {"mode": "raw", "raw": "{\n  \"folioEntrega\": \"{{folioEntrega}}\",\n  \"pedidos\": [\n    {\n      \"folioPedido\": \"{{folioPedido1}}\",\n      \"cliente\": {\n        \"uid\": \"customer_test_001\",\n        \"nombre\": \"<PERSON>\",\n        \"telefono\": \"+52555123456\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"domicilio\": {\n        \"direccion\": \"Av. Insurgentes Sur 123, Col. Roma Norte, CDMX\",\n        \"lat\": 19.4150,\n        \"lng\": -99.1620,\n        \"referencias\": \"Edificio blanco con portón café, tocar timbre del apartamento 4B\"\n      },\n      \"productos\": [\n        {\n          \"id\": \"PROD-001\",\n          \"nombre\": \"Laptop Dell Inspiron 15\",\n          \"cantidad\": 1,\n          \"precio\": 15000.00\n        },\n        {\n          \"id\": \"PROD-002\",\n          \"nombre\": \"Mouse Inalámbrico\",\n          \"cantidad\": 1,\n          \"precio\": 500.00\n        }\n      ],\n      \"total\": 15500.00,\n      \"instrucciones\": \"Entregar en recepción del edificio, mencionar que es para Juan <PERSON>\",\n      \"observaciones\": \"Cliente disponible de 9 AM a 6 PM, preferible entregar por la mañana\"\n    },\n    {\n      \"folioPedido\": \"{{folioPedido2}}\",\n      \"cliente\": {\n        \"uid\": \"customer_test_002\",\n        \"nombre\": \"María González López\",\n        \"telefono\": \"+52555987654\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"domicilio\": {\n        \"direccion\": \"Calle Madero 456, Centro Histórico, CDMX\",\n        \"lat\": 19.4340,\n        \"lng\": -99.1370,\n        \"referencias\": \"Local comercial con letrero azul 'TechStore', entrada por la calle lateral\"\n      },\n      \"productos\": [\n        {\n          \"id\": \"PROD-003\",\n          \"nombre\": \"Impresora HP LaserJet\",\n          \"cantidad\": 1,\n          \"precio\": 4500.00\n        },\n        {\n          \"id\": \"PROD-004\",\n          \"nombre\": \"Cartuchos de Tinta (Pack 4)\",\n          \"cantidad\": 2,\n          \"precio\": 800.00\n        }\n      ],\n      \"total\": 6100.00,\n      \"instrucciones\": \"Tocar timbre y preguntar por María, es el local de computadoras\",\n      \"observaciones\": \"Negocio abierto de 8 AM a 8 PM, evitar horario de comida (2-4 PM)\"\n    }\n  ],\n  \"vehiculo\": {\n    \"id\": \"VEH-TEST-001\",\n    \"tipo\": \"Camioneta de Reparto\",\n    \"placas\": \"ABC-1234\",\n    \"modelo\": \"Ford Transit\",\n    \"marca\": \"Ford\",\n    \"capacidad\": \"1500kg\"\n  },\n  \"chofer\": {\n    \"uid\": \"driver_test_001\",\n    \"nombre\": \"Carlos Roberto Martínez\",\n    \"telefono\": \"+52555777888\",\n    \"pushToken\": \"firebase_push_token_example_123\"\n  },\n  \"observacionesGenerales\": \"Entrega de equipos electrónicos - manejar con cuidado. Ruta optimizada para zona centro y norte de la ciudad. Priorizar entrega comercial en horario de oficina.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/createDeliveryBooking", "host": ["{{base_url}}"], "path": ["createDeliveryBooking"]}, "description": "Crea una entrega agrupada con múltiples pedidos asignados a un conductor y vehículo específico.\n\n**Campos Requeridos:**\n- folioEntrega: Identificador único de la entrega\n- pedidos: Array con al menos un pedido\n- vehiculo: Información del vehículo asignado\n- chofer: Información del conductor asignado\n\n**Respuesta Exitosa:**\n- Crea bookings individuales para cada pedido\n- Genera grupo de entrega con ruta optimizada\n- Asigna conductor y marca como ocupado\n- Envía notificación push al conductor"}}, {"name": "<PERSON><PERSON><PERSON> (Prueba Rápida)", "event": [{"listen": "prerequest", "script": {"exec": ["// Generar folio único simple", "const timestamp = Date.now();", "const folioSimple = `ENT-SIMPLE-${timestamp}`;", "const pedidoSimple = `PED-SIMPLE-${timestamp}`;", "pm.environment.set('folioSimple', folioSimple);", "pm.environment.set('pedidoSimple', pedidoSimple);", "console.log('Folio Simple generado:', folioSimple);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Simple delivery created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData).to.have.property('folioEntrega');", "    console.log('✅ Entrega simple creada:', jsonData.folioEntrega);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Basic {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"folioEntrega\": \"{{folioSimple}}\",\n  \"pedidos\": [\n    {\n      \"folioPedido\": \"{{pedidoSimple}}\",\n      \"cliente\": {\n        \"uid\": \"test_customer_simple\",\n        \"nombre\": \"Cliente de Prueba\",\n        \"telefono\": \"+52555000000\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"domicilio\": {\n        \"direccion\": \"Dirección de Prueba 123\",\n        \"lat\": 19.4326,\n        \"lng\": -99.1332,\n        \"referencias\": \"Casa de prueba\"\n      },\n      \"productos\": [\n        {\n          \"id\": \"PROD-TEST\",\n          \"nombre\": \"Producto de Prueba\",\n          \"cantidad\": 1,\n          \"precio\": 100.00\n        }\n      ],\n      \"total\": 100.00,\n      \"instrucciones\": \"Entrega de prueba\",\n      \"observaciones\": \"Solo para testing\"\n    }\n  ],\n  \"vehiculo\": {\n    \"id\": \"VEH-TEST\",\n    \"tipo\": \"Vehículo de Prueba\",\n    \"placas\": \"TEST-123\",\n    \"modelo\": \"Test Model\",\n    \"marca\": \"Test Brand\"\n  },\n  \"chofer\": {\n    \"uid\": \"test_driver\",\n    \"nombre\": \"Conductor de Prueba\",\n    \"telefono\": \"+52555000001\",\n    \"pushToken\": \"test_token\"\n  },\n  \"observacionesGenerales\": \"Entrega de prueba para validar endpoint\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/createDeliveryBooking", "host": ["{{base_url}}"], "path": ["createDeliveryBooking"]}, "description": "Versión simplificada para pruebas rápidas con datos mínimos requeridos."}}, {"name": "Obtener Detalles de Entrega", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has delivery details', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('folioEntrega');", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('pedidos');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Basic {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/getGroupDeliveryDetails?folioEntrega={{lastFolioEntrega}}", "host": ["{{base_url}}"], "path": ["getGroupDeliveryDetails"], "query": [{"key": "folioEntrega", "value": "{{lastFolioEntrega}}", "description": "Folio de la entrega a consultar"}]}, "description": "Obtiene los detalles completos de una entrega agrupada incluyendo estado, pedido<PERSON>, conductor y veh<PERSON><PERSON>lo asignado."}}, {"name": "Listar Entregas Agrupadas", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Basic {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/getGroupDeliveries", "host": ["{{base_url}}"], "path": ["getGroupDeliveries"]}, "description": "Obtiene la lista de todas las entregas agrupadas en el sistema."}}, {"name": "Actualizar Estado de Pedido", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Update was successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Basic {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"folioEntrega\": \"{{lastFolioEntrega}}\",\n  \"pedidoId\": \"{{folioPedido1}}\",\n  \"status\": \"DELIVERED\",\n  \"evidencia\": {\n    \"foto\": \"https://ejemplo.com/foto-entrega.jpg\",\n    \"firma\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\",\n    \"recibidoPor\": \"<PERSON>\"\n  },\n  \"observaciones\": \"Entrega realizada exitosamente. Cliente satisfecho con el producto.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/updatePedidoDeliveryStatus", "host": ["{{base_url}}"], "path": ["updatePedidoDeliveryStatus"]}, "description": "Actualiza el estado de un pedido específico dentro de una entrega agrupada. Permite agregar evidencia de entrega como fotos, firmas y observaciones."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script global que se ejecuta antes de cada request", "console.log('🚀 E<PERSON><PERSON>ando request en:', new Date().toISOString());", "", "// Verificar que las variables de entorno estén configuradas", "if (!pm.environment.get('base_url')) {", "    console.warn('⚠️ Variable base_url no configurada');", "}", "", "if (!pm.environment.get('auth_token')) {", "    console.warn('⚠️ Variable auth_token no configurada');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script global que se ejecuta después de cada request", "console.log('📊 Response status:', pm.response.status);", "console.log('⏱️ Response time:', pm.response.responseTime + 'ms');", "", "// Log de errores si el status no es exitoso", "if (pm.response.code >= 400) {", "    console.error('❌ Error response:', pm.response.text());", "}"]}}], "variable": [{"key": "base_url", "value": "https://us-central1-balle-813e3.cloudfunctions.net", "type": "string"}]}