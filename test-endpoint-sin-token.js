// 🧪 Script de prueba para el endpoint SIN AUTENTICACIÓN
// Ejecutar con: node test-endpoint-sin-token.js

const https = require('https');

// 🔗 URL del endpoint SIN autenticación
const ENDPOINT_URL = 'https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBookingTest';

console.log('🧪 MODO PRUEBA - ENDPOINT SIN AUTENTICACIÓN');
console.log('===========================================');
console.log('✅ No se requiere token de autenticación');
console.log('✅ Perfecto para testing y desarrollo');
console.log('✅ Todos los datos son marcados como prueba');
console.log('');

// 📦 Datos de prueba para crear una entrega agrupada SIN autenticación
const testData = {
  "folioEntrega": "ENT-TEST-SIN-TOKEN-" + Date.now(),
  "pedidos": [
    {
      "folioPedido": "PED-TEST-001-" + Date.now(),
      "cliente": {
        "uid": "test_customer_001",
        "nombre": "Juan Pérez (CLIENTE PRUEBA)",
        "telefono": "+52555123456",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Av. Insurgentes Sur 123, Col. Roma Norte, CDMX",
        "lat": 19.4150,
        "lng": -99.1620,
        "referencias": "Edificio blanco con portón café - MODO PRUEBA"
      },
      "productos": [
        {
          "id": "PROD-TEST-001",
          "nombre": "Laptop Dell Inspiron (PRUEBA)",
          "cantidad": 1,
          "precio": 15000.00
        },
        {
          "id": "PROD-TEST-002",
          "nombre": "Mouse inalámbrico (PRUEBA)",
          "cantidad": 1,
          "precio": 500.00
        }
      ],
      "total": 15500.00,
      "instrucciones": "Entregar en recepción - MODO PRUEBA",
      "observaciones": "Cliente de prueba - disponible 24/7"
    },
    {
      "folioPedido": "PED-TEST-002-" + Date.now(),
      "cliente": {
        "uid": "test_customer_002",
        "nombre": "María González (CLIENTE PRUEBA)",
        "telefono": "+52555987654",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Calle Madero 456, Centro Histórico, CDMX",
        "lat": 19.4340,
        "lng": -99.1370,
        "referencias": "Local comercial con letrero azul - MODO PRUEBA"
      },
      "productos": [
        {
          "id": "PROD-TEST-003",
          "nombre": "Impresora HP LaserJet (PRUEBA)",
          "cantidad": 1,
          "precio": 3500.00
        },
        {
          "id": "PROD-TEST-004",
          "nombre": "Papel A4 paquete (PRUEBA)",
          "cantidad": 5,
          "precio": 150.00
        }
      ],
      "total": 4250.00,
      "instrucciones": "Tocar timbre y preguntar por María - MODO PRUEBA",
      "observaciones": "Negocio de prueba - abierto 24/7"
    }
  ],
  "vehiculo": {
    "id": "VEH-TEST-001",
    "tipo": "Camioneta de Prueba",
    "placas": "TEST-123",
    "modelo": "Ford Transit (PRUEBA)",
    "marca": "Ford",
    "capacidad": "1500kg"
  },
  "chofer": {
    "uid": "test_driver_001",
    "nombre": "Carlos López (CONDUCTOR PRUEBA)",
    "telefono": "+52555777888",
    "pushToken": "test_firebase_token_carlos_123"
  },
  "observacionesGenerales": "🧪 ENTREGA DE PRUEBA SIN AUTENTICACIÓN - Ruta optimizada para zona centro. Todos los datos son de prueba."
};

// 🚀 Función para hacer la petición HTTP SIN autenticación
function testEndpointSinToken() {
  const postData = JSON.stringify(testData);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
      // 🎉 ¡NO SE REQUIERE HEADER DE AUTORIZACIÓN!
    }
  };

  console.log('🚀 Iniciando prueba del endpoint SIN autenticación...');
  console.log('📍 URL:', ENDPOINT_URL);
  console.log('🔓 Sin headers de autenticación requeridos');
  console.log('📦 Folio de prueba:', testData.folioEntrega);
  console.log('📊 Total de pedidos:', testData.pedidos.length);
  console.log('\n⏳ Enviando petición...\n');

  const req = https.request(ENDPOINT_URL, options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📊 Código de respuesta:', res.statusCode);
      console.log('📋 Headers de respuesta:', res.headers);
      
      try {
        const response = JSON.parse(data);
        console.log('\n✅ Respuesta del servidor:');
        console.log(JSON.stringify(response, null, 2));
        
        if (res.statusCode === 200 && response.success) {
          console.log('\n🎉 ¡Prueba exitosa!');
          console.log('📝 Folio de entrega creado:', response.folioEntrega);
          console.log('📊 Total de pedidos:', response.totalPedidos);
          console.log('🚛 Conductor asignado:', response.choferAsignado.nombre);
          console.log('🚗 Vehículo asignado:', response.vehiculoAsignado.placas);
          console.log('🧪 Modo de prueba:', response.testMode ? 'ACTIVO' : 'INACTIVO');
          console.log('💬 Nota:', response.note);
          
          // Mostrar bookings creados
          console.log('\n📋 Bookings individuales creados:');
          response.bookingsCreados.forEach((booking, index) => {
            console.log(`   ${index + 1}. ID: ${booking.bookingId}`);
            console.log(`      Pedido: ${booking.folioPedido}`);
            console.log(`      Referencia: ${booking.reference}`);
          });
          
        } else {
          console.log('\n❌ Error en la prueba');
          console.log('💬 Mensaje:', response.error || response.message);
        }
      } catch (error) {
        console.log('\n❌ Error al parsear la respuesta JSON:');
        console.log('📄 Respuesta cruda:', data);
        console.log('🐛 Error:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ Error en la petición:');
    console.log('🐛 Error:', error.message);
  });

  req.write(postData);
  req.end();
}

// 📚 Función para mostrar información de uso
function showUsage() {
  console.log(`
📚 GUÍA DE USO DEL ENDPOINT SIN AUTENTICACIÓN

🔗 URL del Endpoint:
${ENDPOINT_URL}

🎉 VENTAJAS:
✅ NO requiere autenticación
✅ Perfecto para testing y desarrollo
✅ Datos marcados automáticamente como prueba
✅ Misma funcionalidad que el endpoint principal
✅ Respuesta inmediata sin configuración

📋 Estructura de Datos (Igual que el endpoint principal):
{
  "folioEntrega": "Identificador único de la entrega",
  "pedidos": [
    {
      "folioPedido": "Folio del pedido individual",
      "cliente": {
        "uid": "ID del cliente",
        "nombre": "Nombre completo",
        "telefono": "Teléfono con código de país",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Dirección completa",
        "lat": 19.4326,
        "lng": -99.1332,
        "referencias": "Referencias adicionales"
      },
      "productos": [...],
      "total": 100.00,
      "instrucciones": "Instrucciones de entrega"
    }
  ],
  "vehiculo": {
    "id": "ID del vehículo",
    "tipo": "Tipo de vehículo",
    "placas": "Placas del vehículo"
  },
  "chofer": {
    "uid": "ID del conductor",
    "nombre": "Nombre del conductor",
    "telefono": "Teléfono del conductor"
  }
}

🎯 Diferencias con el Endpoint Principal:
- ❌ NO requiere header Authorization
- ✅ Todos los bookings marcados con testMode: true
- ✅ Datos guardados en la misma estructura
- ✅ Misma validación de campos requeridos
- ✅ Misma funcionalidad de grupos de entrega

🔧 Uso con curl:
curl -X POST "${ENDPOINT_URL}" \\
  -H "Content-Type: application/json" \\
  -d '{"folioEntrega": "ENT-TEST-123", "pedidos": [...], "vehiculo": {...}, "chofer": {...}}'

🔧 Uso con Postman:
1. Importar: Postman_Collection_SIN_TOKEN.json
2. NO configurar autenticación
3. Ejecutar directamente

⚠️  Nota: 
- Este endpoint es para PRUEBAS y DESARROLLO
- Los datos se guardan en la misma base de datos
- Usar el endpoint principal para producción
- Todos los bookings tienen testMode: true
  `);
}

// 🎯 Ejecutar la prueba
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
} else {
  testEndpointSinToken();
}
