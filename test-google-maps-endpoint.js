// 🗺️ Script de prueba para el endpoint CON GOOGLE MAPS
// Ejecutar con: node test-google-maps-endpoint.js

const https = require('https');

// 🔗 URL del endpoint CON Google Maps
const ENDPOINT_URL = 'https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBookingTest';

console.log('🗺️ ENDPOINT CON GOOGLE MAPS - ENTREGAS AGRUPADAS');
console.log('===============================================');
console.log('✅ Sin autenticación requerida');
console.log('✅ Geocodificación automática de direcciones');
console.log('✅ Optimización de rutas con Google Maps');
console.log('✅ Cálculo de distancias y tiempos reales');
console.log('✅ Coordenadas precisas automáticas');
console.log('');

// 📦 Datos de prueba con direcciones reales para Google Maps
const testDataGoogleMaps = {
  "folioEntrega": "ENT-GMAPS-" + Date.now(),
  "origen": {
    "direccion": "Almacén Central, Av. Insurgentes Sur 1602, Ciudad de México, CDMX",
    "lat": 19.3910,
    "lng": -99.1620
  },
  "pedidos": [
    {
      "folioPedido": "PED-GMAPS-001-" + Date.now(),
      "cliente": {
        "uid": "gmaps_customer_001",
        "nombre": "Juan Pérez (CLIENTE GOOGLE MAPS)",
        "telefono": "+52555123456",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Av. Álvaro Obregón 286, Roma Norte, Cuauhtémoc, 06700 Ciudad de México, CDMX",
        "referencias": "Edificio blanco - Google Maps geocodificará automáticamente"
      },
      "productos": [
        {
          "id": "PROD-GMAPS-001",
          "nombre": "Laptop Dell Inspiron (GOOGLE MAPS)",
          "cantidad": 1,
          "precio": 15000.00
        },
        {
          "id": "PROD-GMAPS-002",
          "nombre": "Mouse inalámbrico (GOOGLE MAPS)",
          "cantidad": 1,
          "precio": 500.00
        }
      ],
      "total": 15500.00,
      "instrucciones": "Entregar en recepción - GEOCODIFICADO CON GOOGLE MAPS",
      "observaciones": "Cliente de prueba - coordenadas automáticas"
    },
    {
      "folioPedido": "PED-GMAPS-002-" + Date.now(),
      "cliente": {
        "uid": "gmaps_customer_002",
        "nombre": "María González (CLIENTE GOOGLE MAPS)",
        "telefono": "+52555987654",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Calle Francisco I. Madero 17, Centro Histórico de la Cdad. de México, Centro, Cuauhtémoc, 06000 Ciudad de México, CDMX",
        "referencias": "Centro Histórico - Ruta optimizada con Google Maps"
      },
      "productos": [
        {
          "id": "PROD-GMAPS-003",
          "nombre": "Impresora HP LaserJet (GOOGLE MAPS)",
          "cantidad": 1,
          "precio": 3500.00
        }
      ],
      "total": 3500.00,
      "instrucciones": "Tocar timbre - DIRECCIÓN GEOCODIFICADA",
      "observaciones": "Ubicación precisa con Google Maps"
    },
    {
      "folioPedido": "PED-GMAPS-003-" + Date.now(),
      "cliente": {
        "uid": "gmaps_customer_003",
        "nombre": "Carlos Rodríguez (CLIENTE GOOGLE MAPS)",
        "telefono": "+52555111333",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Av. Paseo de la Reforma 222, Juárez, Cuauhtémoc, 06600 Ciudad de México, CDMX",
        "referencias": "Zona Rosa - Optimización de ruta automática"
      },
      "productos": [
        {
          "id": "PROD-GMAPS-004",
          "nombre": "Monitor Samsung (GOOGLE MAPS)",
          "cantidad": 1,
          "precio": 4500.00
        }
      ],
      "total": 4500.00,
      "instrucciones": "Entregar en lobby - COORDENADAS PRECISAS",
      "observaciones": "Tercera parada en ruta optimizada"
    }
  ],
  "vehiculo": {
    "id": "VEH-GMAPS-001",
    "tipo": "Camioneta Google Maps",
    "placas": "GMAPS-123",
    "modelo": "Ford Transit (GOOGLE MAPS)",
    "marca": "Ford",
    "capacidad": "1500kg"
  },
  "chofer": {
    "uid": "gmaps_driver_001",
    "nombre": "Roberto Martínez (CONDUCTOR GOOGLE MAPS)",
    "telefono": "+52555777999",
    "pushToken": "gmaps_firebase_token_123"
  },
  "observacionesGenerales": "🗺️ ENTREGA CON GOOGLE MAPS - Geocodificación automática, ruta optimizada, distancias y tiempos reales calculados con Google Maps API."
};

// 🚀 Función para hacer la petición HTTP CON Google Maps
function testGoogleMapsEndpoint() {
  const postData = JSON.stringify(testDataGoogleMaps);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
      // 🎉 ¡NO SE REQUIERE HEADER DE AUTORIZACIÓN!
    }
  };

  console.log('🚀 Iniciando prueba del endpoint CON Google Maps...');
  console.log('📍 URL:', ENDPOINT_URL);
  console.log('🔓 Sin headers de autenticación requeridos');
  console.log('📦 Folio de prueba:', testDataGoogleMaps.folioEntrega);
  console.log('📊 Total de pedidos:', testDataGoogleMaps.pedidos.length);
  console.log('🗺️ Origen:', testDataGoogleMaps.origen.direccion);
  console.log('📍 Destinos a geocodificar:');
  testDataGoogleMaps.pedidos.forEach((pedido, index) => {
    console.log(`   ${index + 1}. ${pedido.domicilio.direccion}`);
  });
  console.log('\n⏳ Enviando petición y esperando geocodificación...\n');

  const req = https.request(ENDPOINT_URL, options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📊 Código de respuesta:', res.statusCode);
      console.log('📋 Headers de respuesta:', res.headers);
      
      try {
        const response = JSON.parse(data);
        console.log('\n✅ Respuesta del servidor:');
        console.log(JSON.stringify(response, null, 2));
        
        if (res.statusCode === 200 && response.success) {
          console.log('\n🎉 ¡Prueba con Google Maps exitosa!');
          console.log('📝 Folio de entrega creado:', response.folioEntrega);
          console.log('📊 Total de pedidos:', response.totalPedidos);
          console.log('🚛 Conductor asignado:', response.choferAsignado.nombre);
          console.log('🚗 Vehículo asignado:', response.vehiculoAsignado.placas);
          console.log('🧪 Modo de prueba:', response.testMode ? 'ACTIVO' : 'INACTIVO');
          console.log('🗺️ Google Maps integrado:', response.googleMapsIntegration ? 'SÍ' : 'NO');
          
          // Mostrar información de Google Maps
          if (response.origen) {
            console.log('\n🗺️ INFORMACIÓN DE GOOGLE MAPS:');
            console.log('📍 Origen geocodificado:');
            console.log(`   Dirección: ${response.origen.direccion}`);
            console.log(`   Coordenadas: ${response.origen.coordenadas.lat}, ${response.origen.coordenadas.lng}`);
            if (response.origen.place_id) {
              console.log(`   Place ID: ${response.origen.place_id}`);
            }
          }
          
          if (response.ruta) {
            console.log('\n🛣️ RUTA OPTIMIZADA:');
            if (response.ruta.calculadaConGoogleMaps) {
              console.log(`   Distancia total: ${response.ruta.distanciaTotal}`);
              console.log(`   Tiempo estimado: ${response.ruta.tiempoEstimado}`);
              if (response.ruta.ordenOptimizado && response.ruta.ordenOptimizado.length > 0) {
                console.log(`   Orden optimizado: ${response.ruta.ordenOptimizado.join(' → ')}`);
              }
            } else {
              console.log(`   ${response.ruta.nota}`);
            }
          }
          
          if (response.destinosGeocoding) {
            console.log('\n📍 DESTINOS GEOCODIFICADOS:');
            response.destinosGeocoding.forEach((destino, index) => {
              console.log(`   ${index + 1}. ${destino.folioPedido}:`);
              console.log(`      Original: ${destino.direccionOriginal}`);
              if (destino.direccionFormateada) {
                console.log(`      Formateada: ${destino.direccionFormateada}`);
              }
              console.log(`      Coordenadas: ${destino.coordenadas.lat}, ${destino.coordenadas.lng}`);
              if (destino.place_id) {
                console.log(`      Place ID: ${destino.place_id}`);
              }
            });
          }
          
          // Mostrar bookings creados
          console.log('\n📋 Bookings individuales creados:');
          response.bookingsCreados.forEach((booking, index) => {
            console.log(`   ${index + 1}. ID: ${booking.bookingId}`);
            console.log(`      Pedido: ${booking.folioPedido}`);
            console.log(`      Referencia: ${booking.reference}`);
          });
          
        } else {
          console.log('\n❌ Error en la prueba');
          console.log('💬 Mensaje:', response.error || response.message);
        }
      } catch (error) {
        console.log('\n❌ Error al parsear la respuesta JSON:');
        console.log('📄 Respuesta cruda:', data);
        console.log('🐛 Error:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ Error en la petición:');
    console.log('🐛 Error:', error.message);
  });

  req.write(postData);
  req.end();
}

// 📚 Función para mostrar información de uso
function showGoogleMapsUsage() {
  console.log(`
📚 GUÍA DE USO DEL ENDPOINT CON GOOGLE MAPS

🔗 URL del Endpoint:
${ENDPOINT_URL}

🗺️ NUEVAS CARACTERÍSTICAS CON GOOGLE MAPS:
✅ Geocodificación automática de direcciones
✅ Optimización de rutas en tiempo real
✅ Cálculo de distancias precisas
✅ Estimación de tiempos de viaje
✅ Coordenadas GPS automáticas
✅ Place IDs de Google Maps
✅ Polylines para visualización de rutas

📋 Estructura de Datos ACTUALIZADA:
{
  "folioEntrega": "Identificador único",
  "origen": {
    "direccion": "Dirección del punto de origen",
    "lat": 19.4326,  // Opcional - se geocodifica si no se proporciona
    "lng": -99.1332  // Opcional - se geocodifica si no se proporciona
  },
  "pedidos": [
    {
      "folioPedido": "Folio del pedido",
      "cliente": {...},
      "domicilio": {
        "direccion": "Dirección completa y precisa",
        // lat y lng son OPCIONALES - Google Maps los calculará automáticamente
        "referencias": "Referencias adicionales"
      },
      "productos": [...],
      "total": 100.00
    }
  ],
  "vehiculo": {...},
  "chofer": {...}
}

🎯 VENTAJAS DE GOOGLE MAPS:
1. 📍 Direcciones más precisas automáticamente
2. 🛣️ Rutas optimizadas para menor tiempo de viaje
3. 📏 Distancias reales calculadas
4. ⏱️ Tiempos de viaje estimados
5. 🗺️ Coordenadas GPS exactas
6. 📱 Compatible con apps de navegación

📊 Respuesta MEJORADA incluye:
{
  "success": true,
  "folioEntrega": "...",
  "origen": {
    "direccion": "Dirección formateada por Google",
    "coordenadas": { "lat": 19.4326, "lng": -99.1332 },
    "place_id": "ChIJ..."
  },
  "ruta": {
    "distanciaTotal": "15.2 km",
    "tiempoEstimado": "45 minutos",
    "ordenOptimizado": [0, 2, 1],
    "calculadaConGoogleMaps": true
  },
  "destinosGeocoding": [
    {
      "folioPedido": "PED-001",
      "direccionOriginal": "Calle 123",
      "direccionFormateada": "Calle 123, Colonia, Ciudad",
      "coordenadas": { "lat": 19.4326, "lng": -99.1332 },
      "place_id": "ChIJ..."
    }
  ],
  "googleMapsIntegration": true
}

🔧 Uso con curl:
curl -X POST "${ENDPOINT_URL}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "folioEntrega": "ENT-GMAPS-123",
    "origen": {
      "direccion": "Almacén Central, Ciudad de México"
    },
    "pedidos": [
      {
        "folioPedido": "PED-001",
        "cliente": {...},
        "domicilio": {
          "direccion": "Av. Insurgentes Sur 123, Roma Norte, CDMX"
        }
      }
    ],
    "vehiculo": {...},
    "chofer": {...}
  }'

⚠️  NOTAS IMPORTANTES:
- Las direcciones deben ser lo más específicas posible
- Incluir colonia, ciudad y código postal mejora la precisión
- Google Maps API tiene límites de uso diarios
- La geocodificación puede tomar unos segundos adicionales
- Si falla la geocodificación, se usan coordenadas por defecto

🎯 ¡Prueba ahora con direcciones reales de Ciudad de México!
  `);
}

// 🎯 Ejecutar la prueba
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showGoogleMapsUsage();
} else {
  testGoogleMapsEndpoint();
}
