#!/bin/bash

# 🧪 Ejemplos de uso del endpoint SIN AUTENTICACIÓN
# ¡No necesitas credenciales para usar este endpoint!

# Configuración
ENDPOINT_URL="https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBookingTest"

echo "🗺️ ENDPOINT SIN AUTENTICACIÓN + GOOGLE MAPS - Entregas Agrupadas"
echo "================================================================="
echo "✅ No se requiere token de autenticación"
echo "✅ Integración completa con Google Maps"
echo "✅ Geocodificación automática de direcciones"
echo "✅ Optimización de rutas en tiempo real"
echo "✅ Cálculo de distancias y tiempos precisos"
echo ""

# Ejemplo 1: Entrega agrupada con Google Maps
echo "📋 Ejemplo 1: Entrega agrupada CON GOOGLE MAPS (SIN TOKEN)"
echo "---------------------------------------------------------"

curl -X POST "$ENDPOINT_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "folioEntrega": "ENT-GMAPS-'$(date +%s)'",
    "origen": {
      "direccion": "Almacén Central, Av. Insurgentes Sur 1602, Ciudad de México, CDMX"
    },
    "pedidos": [
      {
        "folioPedido": "PED-TEST-001-'$(date +%s)'",
        "cliente": {
          "uid": "test_customer_001",
          "nombre": "Juan Pérez (CLIENTE PRUEBA)",
          "telefono": "+52555123456",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Av. Álvaro Obregón 286, Roma Norte, Cuauhtémoc, 06700 Ciudad de México, CDMX",
          "referencias": "Edificio blanco - Google Maps geocodificará automáticamente"
        },
        "productos": [
          {
            "id": "PROD-TEST-001",
            "nombre": "Laptop Dell Inspiron (PRUEBA)",
            "cantidad": 1,
            "precio": 15000.00
          },
          {
            "id": "PROD-TEST-002",
            "nombre": "Mouse inalámbrico (PRUEBA)",
            "cantidad": 1,
            "precio": 500.00
          }
        ],
        "total": 15500.00,
        "instrucciones": "Entregar en recepción - MODO PRUEBA",
        "observaciones": "Cliente de prueba - disponible 24/7"
      },
      {
        "folioPedido": "PED-TEST-002-'$(date +%s)'",
        "cliente": {
          "uid": "test_customer_002",
          "nombre": "María González (CLIENTE PRUEBA)",
          "telefono": "+52555987654",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Calle Madero 456, Centro Histórico, CDMX",
          "lat": 19.4340,
          "lng": -99.1370,
          "referencias": "Local comercial con letrero azul - MODO PRUEBA"
        },
        "productos": [
          {
            "id": "PROD-TEST-003",
            "nombre": "Impresora HP LaserJet (PRUEBA)",
            "cantidad": 1,
            "precio": 3500.00
          }
        ],
        "total": 3500.00,
        "instrucciones": "Tocar timbre y preguntar por María - MODO PRUEBA",
        "observaciones": "Negocio de prueba - abierto 24/7"
      }
    ],
    "vehiculo": {
      "id": "VEH-TEST-001",
      "tipo": "Camioneta de Prueba",
      "placas": "TEST-123",
      "modelo": "Ford Transit (PRUEBA)",
      "marca": "Ford",
      "capacidad": "1500kg"
    },
    "chofer": {
      "uid": "test_driver_001",
      "nombre": "Carlos López (CONDUCTOR PRUEBA)",
      "telefono": "+52555777888",
      "pushToken": "test_firebase_token_carlos_123"
    },
    "observacionesGenerales": "🧪 ENTREGA DE PRUEBA SIN AUTENTICACIÓN - Ruta optimizada para zona centro."
  }'

echo ""
echo ""

# Ejemplo 2: Entrega simple de prueba
echo "📋 Ejemplo 2: Entrega simple de PRUEBA (SIN TOKEN)"
echo "-------------------------------------------------"

curl -X POST "$ENDPOINT_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "folioEntrega": "ENT-SIMPLE-TEST-'$(date +%s)'",
    "pedidos": [
      {
        "folioPedido": "PED-SIMPLE-TEST-'$(date +%s)'",
        "cliente": {
          "uid": "test_customer_simple",
          "nombre": "Ana García (CLIENTE SIMPLE PRUEBA)",
          "telefono": "+52555111222",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Av. Reforma 789, Col. Juárez, CDMX",
          "lat": 19.4260,
          "lng": -99.1700,
          "referencias": "Torre de oficinas, piso 15 - MODO PRUEBA"
        },
        "productos": [
          {
            "id": "PROD-SIMPLE-TEST",
            "nombre": "Documento importante (PRUEBA)",
            "cantidad": 1,
            "precio": 0.00
          }
        ],
        "total": 50.00,
        "instrucciones": "Entregar en recepción, mencionar empresa TechCorp - MODO PRUEBA",
        "observaciones": "🧪 Entrega urgente de prueba - disponible 24/7"
      }
    ],
    "vehiculo": {
      "id": "VEH-TEST-002",
      "tipo": "Motocicleta de Prueba",
      "placas": "MOTO-TEST",
      "modelo": "Honda CBR (PRUEBA)",
      "marca": "Honda"
    },
    "chofer": {
      "uid": "test_driver_002",
      "nombre": "Roberto Martínez (CONDUCTOR PRUEBA)",
      "telefono": "+52555333444",
      "pushToken": "test_firebase_token_roberto_456"
    },
    "observacionesGenerales": "🧪 ENTREGA EXPRESS DE PRUEBA - documento urgente sin autenticación"
  }'

echo ""
echo ""

# Ejemplo 3: Prueba con datos mínimos
echo "📋 Ejemplo 3: Datos mínimos requeridos (SIN TOKEN)"
echo "-------------------------------------------------"

curl -X POST "$ENDPOINT_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "folioEntrega": "ENT-MINIMAL-TEST-'$(date +%s)'",
    "pedidos": [
      {
        "folioPedido": "PED-MINIMAL-'$(date +%s)'",
        "cliente": {
          "uid": "test_minimal",
          "nombre": "Cliente Mínimo (PRUEBA)"
        },
        "domicilio": {
          "direccion": "Dirección de prueba mínima",
          "lat": 19.4326,
          "lng": -99.1332
        }
      }
    ],
    "vehiculo": {
      "id": "VEH-MINIMAL",
      "tipo": "Vehículo Mínimo"
    },
    "chofer": {
      "uid": "test_driver_minimal",
      "nombre": "Conductor Mínimo (PRUEBA)"
    }
  }'

echo ""
echo ""

# Información adicional
echo "ℹ️  Información del Endpoint SIN AUTENTICACIÓN"
echo "=============================================="
echo ""
echo "🎉 VENTAJAS:"
echo "   ✅ No requiere credenciales de autenticación"
echo "   ✅ Perfecto para testing y desarrollo"
echo "   ✅ Misma funcionalidad que el endpoint principal"
echo "   ✅ Datos marcados automáticamente como prueba"
echo "   ✅ Respuesta inmediata sin configuración"
echo ""
echo "📊 Códigos de Respuesta:"
echo "   - 200: Éxito - Entrega creada correctamente"
echo "   - 400: Error en los datos enviados"
echo "   - 500: Error interno del servidor"
echo ""
echo "🔄 Estados del Sistema (Iguales al endpoint principal):"
echo "   - Booking: NEW → ACCEPTED → REACHED → PENDING → PAID → COMPLETE"
echo "   - Grupo: PENDING → ASSIGNED → IN_PROGRESS → COMPLETED"
echo "   - Pedido: PENDING → DELIVERED"
echo ""
echo "🗄️  Base de Datos:"
echo "   - Misma estructura que el endpoint principal"
echo "   - Bookings marcados con testMode: true"
echo "   - Guardado en /bookings/{bookingId}"
echo "   - Grupos en /deliveryGroups/{folioEntrega}"
echo ""
echo "📋 Campos Requeridos Mínimos:"
echo "   - folioEntrega: string"
echo "   - pedidos: array con al menos 1 elemento"
echo "     - folioPedido: string"
echo "     - cliente.uid: string"
echo "     - cliente.nombre: string"
echo "     - domicilio.direccion: string"
echo "     - domicilio.lat: number"
echo "     - domicilio.lng: number"
echo "   - vehiculo.id: string"
echo "   - vehiculo.tipo: string"
echo "   - chofer.uid: string"
echo "   - chofer.nombre: string"
echo ""
echo "🧪 Diferencias con el Endpoint Principal:"
echo "   ❌ NO requiere header Authorization"
echo "   ✅ Todos los bookings marcados con testMode: true"
echo "   ✅ Usuario por defecto: test_admin_user"
echo "   ✅ Logs adicionales para debugging"
echo ""
echo "🔧 Para usar con otras herramientas:"
echo ""
echo "   📱 Postman:"
echo "   - Importar: Postman_Collection_SIN_TOKEN.json"
echo "   - NO configurar autenticación"
echo "   - Ejecutar directamente"
echo ""
echo "   🖥️  JavaScript/Node.js:"
echo "   - node test-endpoint-sin-token.js"
echo ""
echo "   🌐 Navegador (fetch):"
echo "   fetch('$ENDPOINT_URL', {"
echo "     method: 'POST',"
echo "     headers: { 'Content-Type': 'application/json' },"
echo "     body: JSON.stringify(data)"
echo "   })"
echo ""
echo "⚠️  Notas Importantes:"
echo "   - Este endpoint es para PRUEBAS y DESARROLLO"
echo "   - Los datos se guardan en la misma base de datos"
echo "   - Para producción usar el endpoint principal con autenticación"
echo "   - Todos los bookings tienen testMode: true para identificación"
echo ""
echo "🎯 ¡Listo para probar sin configuración adicional!"
