import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    RefreshControl,
    Alert,
    Dimensions
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-native-elements';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../common/theme';
import { api } from 'common';

const { width } = Dimensions.get('window');

export default function GroupDeliveryListScreen(props) {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    
    const [deliveries, setDeliveries] = useState([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    
    const auth = useSelector(state => state.auth);

    useEffect(() => {
        loadDeliveries();
    }, []);

    const loadDeliveries = async () => {
        try {
            setLoading(true);
            const response = await fetch(`https://${api.config.projectId}.cloudfunctions.net/getGroupDeliveries`, {
                method: 'GET',
                headers: {
                    'Authorization': `Basic ${btoa(api.config.projectId + ':' + auth.accessToken)}`
                }
            });

            const result = await response.json();
            
            if (result.success) {
                // Filtrar solo las entregas del conductor actual
                const driverDeliveries = result.deliveries.filter(
                    delivery => delivery.chofer.uid === auth.profile.uid
                );
                setDeliveries(driverDeliveries);
            } else {
                Alert.alert(t('error'), result.error);
            }
        } catch (error) {
            Alert.alert(t('error'), error.message);
        } finally {
            setLoading(false);
        }
    };

    const onRefresh = async () => {
        setRefreshing(true);
        await loadDeliveries();
        setRefreshing(false);
    };

    const navigateToDelivery = (folioEntrega) => {
        props.navigation.navigate('GroupDelivery', { folioEntrega });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'PENDING': return colors.YELLOW;
            case 'IN_PROGRESS': return colors.BLUE;
            case 'COMPLETED': return colors.GREEN;
            case 'CANCELLED': return colors.RED;
            default: return colors.GRAY;
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'PENDING': return 'schedule';
            case 'IN_PROGRESS': return 'local-shipping';
            case 'COMPLETED': return 'check-circle';
            case 'CANCELLED': return 'cancel';
            default: return 'help';
        }
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const renderDeliveryItem = ({ item }) => {
        const progressPercentage = item.totalPedidos > 0 ? 
            (item.pedidosEntregados / item.totalPedidos) * 100 : 0;

        return (
            <TouchableOpacity 
                style={styles.deliveryItem}
                onPress={() => navigateToDelivery(item.folioEntrega)}
            >
                <View style={styles.deliveryHeader}>
                    <View style={styles.folioContainer}>
                        <Text style={styles.folioText}>{item.folioEntrega}</Text>
                        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
                            <MaterialIcons 
                                name={getStatusIcon(item.status)} 
                                size={16} 
                                color={colors.WHITE} 
                            />
                            <Text style={styles.statusText}>{t(item.status)}</Text>
                        </View>
                    </View>
                </View>

                <View style={styles.deliveryInfo}>
                    <View style={styles.infoRow}>
                        <MaterialIcons name="event" size={16} color={colors.GRAY} />
                        <Text style={styles.infoText}>
                            {t('created')}: {formatDate(item.fechaCreacion)}
                        </Text>
                    </View>

                    {item.fechaInicio && (
                        <View style={styles.infoRow}>
                            <MaterialIcons name="play-arrow" size={16} color={colors.GRAY} />
                            <Text style={styles.infoText}>
                                {t('started')}: {formatDate(item.fechaInicio)}
                            </Text>
                        </View>
                    )}

                    <View style={styles.infoRow}>
                        <MaterialIcons name="local-shipping" size={16} color={colors.GRAY} />
                        <Text style={styles.infoText}>
                            {item.vehiculo.tipo} - {item.vehiculo.placas}
                        </Text>
                    </View>

                    <View style={styles.progressContainer}>
                        <View style={styles.progressInfo}>
                            <Text style={styles.progressText}>
                                {t('progress')}: {item.pedidosEntregados}/{item.totalPedidos}
                            </Text>
                            <Text style={styles.progressPercentage}>
                                {progressPercentage.toFixed(0)}%
                            </Text>
                        </View>
                        <View style={styles.progressBar}>
                            <View 
                                style={[
                                    styles.progressFill, 
                                    { 
                                        width: `${progressPercentage}%`,
                                        backgroundColor: getStatusColor(item.status)
                                    }
                                ]} 
                            />
                        </View>
                    </View>

                    {item.ruta && (
                        <View style={styles.routeInfo}>
                            <MaterialIcons name="route" size={16} color={colors.GRAY} />
                            <Text style={styles.infoText}>
                                {item.ruta.distanciaTotal.toFixed(1)} km - {item.ruta.tiempoEstimado} min
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.deliveryActions}>
                    <TouchableOpacity 
                        style={styles.actionButton}
                        onPress={() => navigateToDelivery(item.folioEntrega)}
                    >
                        <MaterialIcons name="visibility" size={20} color={colors.WHITE} />
                        <Text style={styles.actionButtonText}>{t('view_details')}</Text>
                    </TouchableOpacity>
                </View>
            </TouchableOpacity>
        );
    };

    const renderEmptyList = () => (
        <View style={styles.emptyContainer}>
            <MaterialIcons name="local-shipping" size={64} color={colors.GRAY} />
            <Text style={styles.emptyText}>{t('no_group_deliveries')}</Text>
            <Text style={styles.emptySubtext}>{t('group_deliveries_will_appear_here')}</Text>
            <Button
                title={t('refresh')}
                onPress={loadDeliveries}
                buttonStyle={styles.refreshButton}
                loading={loading}
            />
        </View>
    );

    return (
        <View style={styles.container}>
            <Header
                centerComponent={{
                    text: t('group_deliveries'),
                    style: { color: colors.WHITE, fontSize: 18, fontWeight: 'bold' }
                }}
                backgroundColor={colors.HEADER}
                leftComponent={{
                    icon: 'menu',
                    color: colors.WHITE,
                    onPress: () => props.navigation.openDrawer()
                }}
                rightComponent={{
                    icon: 'refresh',
                    color: colors.WHITE,
                    onPress: loadDeliveries
                }}
            />

            <FlatList
                data={deliveries}
                renderItem={renderDeliveryItem}
                keyExtractor={(item) => item.folioEntrega}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={[colors.HEADER]}
                    />
                }
                ListEmptyComponent={renderEmptyList}
                contentContainerStyle={deliveries.length === 0 ? styles.emptyListContainer : null}
                showsVerticalScrollIndicator={false}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    deliveryItem: {
        backgroundColor: colors.WHITE,
        margin: 10,
        borderRadius: 12,
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        overflow: 'hidden',
    },
    deliveryHeader: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: colors.BORDER_BACKGROUND,
    },
    folioContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    folioText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.BLACK,
    },
    statusBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        color: colors.WHITE,
        fontSize: 12,
        fontWeight: 'bold',
        marginLeft: 4,
    },
    deliveryInfo: {
        padding: 15,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    infoText: {
        fontSize: 14,
        color: colors.GRAY,
        marginLeft: 8,
        flex: 1,
    },
    progressContainer: {
        marginTop: 10,
        marginBottom: 5,
    },
    progressInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 5,
    },
    progressText: {
        fontSize: 14,
        fontWeight: '600',
        color: colors.BLACK,
    },
    progressPercentage: {
        fontSize: 14,
        fontWeight: 'bold',
        color: colors.HEADER,
    },
    progressBar: {
        height: 6,
        backgroundColor: colors.LIGHT_GRAY,
        borderRadius: 3,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        borderRadius: 3,
    },
    routeInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5,
    },
    deliveryActions: {
        padding: 15,
        borderTopWidth: 1,
        borderTopColor: colors.BORDER_BACKGROUND,
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.HEADER,
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 8,
    },
    actionButtonText: {
        color: colors.WHITE,
        fontSize: 14,
        fontWeight: 'bold',
        marginLeft: 5,
    },
    emptyListContainer: {
        flex: 1,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.GRAY,
        marginTop: 20,
        textAlign: 'center',
    },
    emptySubtext: {
        fontSize: 14,
        color: colors.GRAY,
        marginTop: 10,
        textAlign: 'center',
        lineHeight: 20,
    },
    refreshButton: {
        backgroundColor: colors.HEADER,
        marginTop: 20,
        paddingHorizontal: 30,
    },
});
