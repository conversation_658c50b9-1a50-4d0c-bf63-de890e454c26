#!/bin/bash

# 📦 Ejemplos de uso del endpoint createDeliveryBooking con curl
# Asegúrate de tener las credenciales de autenticación correctas

# Configuración
ENDPOINT_URL="https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBooking"
# Reemplaza con tus credenciales reales
USERNAME="tu_usuario"
PASSWORD="tu_password"

echo "🚀 Ejemplos de uso del endpoint createDeliveryBooking"
echo "=================================================="

# Ejemplo 1: Entrega simple con un pedido
echo ""
echo "📋 Ejemplo 1: Entrega simple con un pedido"
echo "-------------------------------------------"

curl -X POST "$ENDPOINT_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $(echo -n $USERNAME:$PASSWORD | base64)" \
  -d '{
    "folioEntrega": "ENT-SIMPLE-'$(date +%s)'",
    "pedidos": [
      {
        "folioPedido": "PED-SIMPLE-001",
        "cliente": {
          "uid": "customer_simple_001",
          "nombre": "Ana García",
          "telefono": "+52555123456",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Av. Insurgentes 123, Col. Roma Norte",
          "lat": 19.4150,
          "lng": -99.1620,
          "referencias": "Edificio blanco, portón café"
        },
        "productos": [
          {
            "id": "PROD-001",
            "nombre": "Laptop Dell",
            "cantidad": 1,
            "precio": 15000.00
          }
        ],
        "total": 15000.00,
        "instrucciones": "Entregar en recepción del edificio",
        "observaciones": "Cliente disponible de 9 AM a 6 PM"
      }
    ],
    "vehiculo": {
      "id": "VEH-001",
      "tipo": "Motocicleta",
      "placas": "MOT-123",
      "modelo": "Honda CBR",
      "marca": "Honda"
    },
    "chofer": {
      "uid": "driver_001",
      "nombre": "Roberto Martínez",
      "telefono": "+52555987654",
      "pushToken": "token_roberto_123"
    },
    "observacionesGenerales": "Entrega de equipo electrónico - manejar con cuidado"
  }'

echo ""
echo ""

# Ejemplo 2: Entrega múltiple con varios pedidos
echo "📋 Ejemplo 2: Entrega múltiple con varios pedidos"
echo "------------------------------------------------"

curl -X POST "$ENDPOINT_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $(echo -n $USERNAME:$PASSWORD | base64)" \
  -d '{
    "folioEntrega": "ENT-MULTIPLE-'$(date +%s)'",
    "pedidos": [
      {
        "folioPedido": "PED-MULTI-001",
        "cliente": {
          "uid": "customer_multi_001",
          "nombre": "Carlos Rodríguez",
          "telefono": "+52555111222",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Calle Madero 456, Centro Histórico",
          "lat": 19.4340,
          "lng": -99.1370,
          "referencias": "Local comercial con letrero azul"
        },
        "productos": [
          {
            "id": "PROD-002",
            "nombre": "Impresora HP",
            "cantidad": 1,
            "precio": 3500.00
          },
          {
            "id": "PROD-003",
            "nombre": "Papel A4",
            "cantidad": 5,
            "precio": 150.00
          }
        ],
        "total": 4250.00,
        "instrucciones": "Tocar timbre y preguntar por Carlos",
        "observaciones": "Negocio abierto de 8 AM a 8 PM"
      },
      {
        "folioPedido": "PED-MULTI-002",
        "cliente": {
          "uid": "customer_multi_002",
          "nombre": "Laura Fernández",
          "telefono": "+52555333444",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Av. Reforma 789, Col. Juárez",
          "lat": 19.4260,
          "lng": -99.1700,
          "referencias": "Torre de oficinas, piso 15"
        },
        "productos": [
          {
            "id": "PROD-004",
            "nombre": "Monitor Samsung",
            "cantidad": 2,
            "precio": 4500.00
          }
        ],
        "total": 9000.00,
        "instrucciones": "Entregar en recepción, mencionar empresa TechCorp",
        "observaciones": "Horario de oficina: 9 AM a 6 PM"
      },
      {
        "folioPedido": "PED-MULTI-003",
        "cliente": {
          "uid": "customer_multi_003",
          "nombre": "Miguel Santos",
          "telefono": "+52555555666",
          "email": "<EMAIL>"
        },
        "domicilio": {
          "direccion": "Calle Revolución 321, Col. San Ángel",
          "lat": 19.3500,
          "lng": -99.1900,
          "referencias": "Casa con jardín frontal"
        },
        "productos": [
          {
            "id": "PROD-005",
            "nombre": "Teclado Mecánico",
            "cantidad": 1,
            "precio": 2200.00
          },
          {
            "id": "PROD-006",
            "nombre": "Mouse Gaming",
            "cantidad": 1,
            "precio": 1800.00
          }
        ],
        "total": 4000.00,
        "instrucciones": "Entregar directamente al cliente",
        "observaciones": "Cliente trabaja desde casa, disponible todo el día"
      }
    ],
    "vehiculo": {
      "id": "VEH-002",
      "tipo": "Camioneta",
      "placas": "CAM-456",
      "modelo": "Ford Transit",
      "marca": "Ford",
      "capacidad": "1500kg"
    },
    "chofer": {
      "uid": "driver_002",
      "nombre": "Patricia López",
      "telefono": "+52555777888",
      "pushToken": "token_patricia_456"
    },
    "observacionesGenerales": "Ruta optimizada para zona centro y sur de la ciudad. Priorizar entregas comerciales en horario de oficina."
  }'

echo ""
echo ""

# Ejemplo 3: Verificar estado de una entrega (ejemplo de otro endpoint)
echo "📋 Ejemplo 3: Verificar detalles de entrega (endpoint relacionado)"
echo "----------------------------------------------------------------"

# Nota: Este sería un ejemplo de cómo consultar los detalles después de crear la entrega
echo "# Para consultar detalles de una entrega creada:"
echo "curl -X GET 'https://us-central1-balle-813e3.cloudfunctions.net/getGroupDeliveryDetails?folioEntrega=ENT-EJEMPLO-123' \\"
echo "  -H 'Authorization: Basic \$(echo -n \$USERNAME:\$PASSWORD | base64)'"

echo ""
echo ""

# Información adicional
echo "ℹ️  Información Adicional"
echo "========================"
echo ""
echo "🔑 Autenticación:"
echo "   - Reemplaza USERNAME y PASSWORD con tus credenciales reales"
echo "   - Las credenciales deben ser proporcionadas por el administrador del sistema"
echo ""
echo "📊 Códigos de Respuesta:"
echo "   - 200: Éxito - Entrega creada correctamente"
echo "   - 400: Error en los datos enviados"
echo "   - 401: No autorizado - Credenciales incorrectas"
echo "   - 500: Error interno del servidor"
echo ""
echo "🔄 Estados del Sistema:"
echo "   - Booking: NEW → ACCEPTED → REACHED → PENDING → PAID → COMPLETE"
echo "   - Grupo: PENDING → ASSIGNED → IN_PROGRESS → COMPLETED"
echo "   - Pedido: PENDING → DELIVERED"
echo ""
echo "📱 Notificaciones:"
echo "   - Se envía automáticamente push notification al conductor"
echo "   - El conductor aparece como ocupado (queue: true) en el sistema"
echo ""
echo "🗄️  Base de Datos:"
echo "   - Bookings individuales: /bookings/{bookingId}"
echo "   - Grupos de entrega: /deliveryGroups/{folioEntrega}"
echo "   - Información de usuarios: /users/{uid}"
echo ""
echo "⚠️  Requisitos:"
echo "   - El conductor debe existir en la base de datos"
echo "   - El conductor debe estar disponible (queue: false)"
echo "   - Si se proporciona ID de vehículo, debe existir en /cars/{id}"
echo ""
echo "🧪 Para probar sin autenticación real, usa el script de Node.js:"
echo "   node test-endpoint-delivery.js"
