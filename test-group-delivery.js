/**
 * Script de pruebas para el Sistema de Entregas Agrupadas
 * 
 * Este script permite probar las APIs del sistema de entregas agrupadas
 * Ejecutar con: node test-group-delivery.js
 */

const https = require('https');

// Configuración
const CONFIG = {
  projectId: 'tu-proyecto-firebase', // Cambiar por tu project ID
  baseUrl: 'https://tu-proyecto-firebase.cloudfunctions.net',
  authToken: 'tu-token-de-autenticacion' // Cambiar por token válido
};

// Datos de prueba
const TEST_DATA = {
  groupDelivery: {
    pedidos: {
      "TEST-PED-001": {
        folioPedido: "TEST-PED-001",
        cliente: {
          uid: "test_customer_001",
          nombre: "Cliente de Prueba 1",
          telefono: "+52123456789"
        },
        domicilio: {
          direccion: "Av. Test 123, Col. Prueba",
          lat: 19.4326,
          lng: -99.1332,
          referencias: "Casa de prueba"
        },
        productos: [
          {
            id: "PROD-TEST-001",
            nombre: "Producto de Prueba A",
            cantidad: 2,
            precio: 100.00
          }
        ],
        total: 200.00,
        statusEntrega: "PENDING",
        observaciones: "",
        horaEntrega: null,
        evidenciaEntrega: {
          foto: "",
          firma: "",
          recibidoPor: ""
        }
      },
      "TEST-PED-002": {
        folioPedido: "TEST-PED-002",
        cliente: {
          uid: "test_customer_002",
          nombre: "Cliente de Prueba 2",
          telefono: "+52987654321"
        },
        domicilio: {
          direccion: "Calle Test 456, Col. Ejemplo",
          lat: 19.4400,
          lng: -99.1280,
          referencias: "Edificio de prueba"
        },
        productos: [
          {
            id: "PROD-TEST-002",
            nombre: "Producto de Prueba B",
            cantidad: 1,
            precio: 150.00
          }
        ],
        total: 150.00,
        statusEntrega: "PENDING",
        observaciones: "",
        horaEntrega: null,
        evidenciaEntrega: {
          foto: "",
          firma: "",
          recibidoPor: ""
        }
      }
    },
    vehiculo: {
      id: "TEST-VEH-001",
      tipo: "Camión de Prueba",
      placas: "TEST-123",
      capacidad: "500kg"
    },
    chofer: {
      uid: "test_driver_001",
      nombre: "Conductor de Prueba",
      telefono: "+52555123456",
      pushToken: "ExponentPushToken[test_token]",
      userPlatform: "Android",
      fleetadmin: null
    },
    observacionesGenerales: "Entrega de prueba del sistema"
  }
};

// Función auxiliar para hacer requests HTTP
function makeRequest(endpoint, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = `${CONFIG.baseUrl}${endpoint}`;
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${Buffer.from(CONFIG.projectId + ':' + CONFIG.authToken).toString('base64')}`
      }
    };

    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: parsedData
          });
        } catch (error) {
          reject(new Error(`Error parsing response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Pruebas del sistema
async function runTests() {
  console.log('🚀 Iniciando pruebas del Sistema de Entregas Agrupadas\n');
  
  let createdFolioEntrega = null;

  try {
    // Test 1: Crear entrega agrupada
    console.log('📦 Test 1: Crear entrega agrupada');
    const createResponse = await makeRequest('/createGroupDelivery', 'POST', TEST_DATA.groupDelivery);
    
    if (createResponse.statusCode === 200 && createResponse.data.success) {
      createdFolioEntrega = createResponse.data.folioEntrega;
      console.log(`✅ Entrega creada exitosamente: ${createdFolioEntrega}`);
      console.log(`   Total pedidos: ${createResponse.data.totalPedidos}\n`);
    } else {
      console.log(`❌ Error creando entrega: ${createResponse.data.error}\n`);
      return;
    }

    // Test 2: Obtener lista de entregas agrupadas
    console.log('📋 Test 2: Obtener lista de entregas agrupadas');
    const listResponse = await makeRequest('/getGroupDeliveries');
    
    if (listResponse.statusCode === 200 && listResponse.data.success) {
      console.log(`✅ Lista obtenida exitosamente`);
      console.log(`   Total entregas: ${listResponse.data.deliveries.length}\n`);
    } else {
      console.log(`❌ Error obteniendo lista: ${listResponse.data.error}\n`);
    }

    // Test 3: Obtener detalles de entrega específica
    console.log('🔍 Test 3: Obtener detalles de entrega específica');
    const detailsResponse = await makeRequest(`/getGroupDeliveryDetails?folioEntrega=${createdFolioEntrega}`);
    
    if (detailsResponse.statusCode === 200 && detailsResponse.data.success) {
      console.log(`✅ Detalles obtenidos exitosamente`);
      console.log(`   Estado: ${detailsResponse.data.delivery.status}`);
      console.log(`   Conductor: ${detailsResponse.data.delivery.chofer.nombre}\n`);
    } else {
      console.log(`❌ Error obteniendo detalles: ${detailsResponse.data.error}\n`);
    }

    // Test 4: Actualizar estado de entrega a IN_PROGRESS
    console.log('🚛 Test 4: Actualizar estado de entrega a IN_PROGRESS');
    const updateStatusResponse = await makeRequest('/updateGroupDeliveryStatus', 'POST', {
      folioEntrega: createdFolioEntrega,
      status: 'IN_PROGRESS',
      observaciones: 'Iniciando ruta de entrega'
    });
    
    if (updateStatusResponse.statusCode === 200 && updateStatusResponse.data.success) {
      console.log(`✅ Estado actualizado exitosamente a IN_PROGRESS\n`);
    } else {
      console.log(`❌ Error actualizando estado: ${updateStatusResponse.data.error}\n`);
    }

    // Test 5: Marcar primer pedido como entregado
    console.log('📦 Test 5: Marcar primer pedido como entregado');
    const updatePedidoResponse = await makeRequest('/updatePedidoDeliveryStatus', 'POST', {
      folioEntrega: createdFolioEntrega,
      pedidoId: 'TEST-PED-001',
      status: 'DELIVERED',
      evidencia: {
        foto: 'https://example.com/test-photo.jpg',
        recibidoPor: 'Cliente de Prueba 1'
      },
      observaciones: 'Entregado sin problemas'
    });
    
    if (updatePedidoResponse.statusCode === 200 && updatePedidoResponse.data.success) {
      console.log(`✅ Pedido TEST-PED-001 marcado como entregado\n`);
    } else {
      console.log(`❌ Error marcando pedido: ${updatePedidoResponse.data.error}\n`);
    }

    // Test 6: Marcar segundo pedido como entregado
    console.log('📦 Test 6: Marcar segundo pedido como entregado');
    const updatePedido2Response = await makeRequest('/updatePedidoDeliveryStatus', 'POST', {
      folioEntrega: createdFolioEntrega,
      pedidoId: 'TEST-PED-002',
      status: 'DELIVERED',
      evidencia: {
        foto: 'https://example.com/test-photo2.jpg',
        recibidoPor: 'Cliente de Prueba 2'
      },
      observaciones: 'Entregado correctamente'
    });
    
    if (updatePedido2Response.statusCode === 200 && updatePedido2Response.data.success) {
      console.log(`✅ Pedido TEST-PED-002 marcado como entregado\n`);
    } else {
      console.log(`❌ Error marcando pedido: ${updatePedido2Response.data.error}\n`);
    }

    // Test 7: Verificar que la entrega se completó automáticamente
    console.log('✅ Test 7: Verificar completación automática de entrega');
    const finalDetailsResponse = await makeRequest(`/getGroupDeliveryDetails?folioEntrega=${createdFolioEntrega}`);
    
    if (finalDetailsResponse.statusCode === 200 && finalDetailsResponse.data.success) {
      const delivery = finalDetailsResponse.data.delivery;
      console.log(`✅ Estado final de entrega: ${delivery.status}`);
      console.log(`   Pedidos entregados: ${delivery.pedidosEntregados}/${delivery.totalPedidos}`);
      
      if (delivery.status === 'COMPLETED') {
        console.log(`✅ Entrega completada automáticamente\n`);
      } else {
        console.log(`⚠️  Entrega no se completó automáticamente (estado: ${delivery.status})\n`);
      }
    } else {
      console.log(`❌ Error verificando estado final: ${finalDetailsResponse.data.error}\n`);
    }

    // Test 8: Generar reporte
    console.log('📊 Test 8: Generar reporte de entregas');
    const reportResponse = await makeRequest('/getGroupDeliveryReport');
    
    if (reportResponse.statusCode === 200 && reportResponse.data.success) {
      console.log(`✅ Reporte generado exitosamente`);
      console.log(`   Total entregas: ${reportResponse.data.statistics.totalEntregas}`);
      console.log(`   Entregas completadas: ${reportResponse.data.statistics.entregasCompletadas}`);
      console.log(`   Total pedidos: ${reportResponse.data.statistics.totalPedidos}`);
      console.log(`   Pedidos entregados: ${reportResponse.data.statistics.pedidosEntregados}\n`);
    } else {
      console.log(`❌ Error generando reporte: ${reportResponse.data.error}\n`);
    }

    console.log('🎉 Todas las pruebas completadas exitosamente!');
    console.log(`📋 Folio de entrega de prueba: ${createdFolioEntrega}`);

  } catch (error) {
    console.error('❌ Error durante las pruebas:', error.message);
  }
}

// Función para limpiar datos de prueba
async function cleanupTestData() {
  console.log('\n🧹 Limpiando datos de prueba...');
  console.log('⚠️  Nota: La limpieza debe hacerse manualmente desde la consola de Firebase');
  console.log('   o implementar una función de limpieza en el backend.');
}

// Ejecutar pruebas
if (require.main === module) {
  console.log('⚙️  Configuración:');
  console.log(`   Project ID: ${CONFIG.projectId}`);
  console.log(`   Base URL: ${CONFIG.baseUrl}`);
  console.log(`   Auth Token: ${CONFIG.authToken ? '***configurado***' : '❌ NO CONFIGURADO'}\n`);
  
  if (!CONFIG.authToken || CONFIG.authToken === 'tu-token-de-autenticacion') {
    console.log('❌ Error: Debes configurar un token de autenticación válido en CONFIG.authToken');
    process.exit(1);
  }
  
  runTests()
    .then(() => cleanupTestData())
    .catch(console.error);
}

module.exports = {
  runTests,
  makeRequest,
  TEST_DATA,
  CONFIG
};
