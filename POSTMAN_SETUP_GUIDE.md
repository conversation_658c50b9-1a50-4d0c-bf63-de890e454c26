# 📮 Guía de Configuración de Postman - Sistema de Entregas Agrupadas

## 🎯 Archivos Incluidos

1. **`Postman_Collection_Entregas_Agrupadas.json`** - Colección principal con todos los endpoints
2. **`Postman_Environment_Entregas.json`** - Variables de entorno preconfiguradas
3. **`POSTMAN_SETUP_GUIDE.md`** - Esta guía de configuración

---

## 🚀 Configuración Paso a Paso

### **Paso 1: Importar la Colección**

1. Abre **Postman**
2. Haz clic en **"Import"** (botón superior izquierdo)
3. Selecciona **"Upload Files"**
4. Arrastra o selecciona el archivo `Postman_Collection_Entregas_Agrupadas.json`
5. Haz clic en **"Import"**

### **Paso 2: Importar el Ambiente**

1. En Postman, ve a **"Environments"** (icono de engranaje en la esquina superior derecha)
2. Haz clic en **"Import"**
3. Selecciona el archivo `Postman_Environment_Entregas.json`
4. Haz clic en **"Import"**

### **Paso 3: Activar el Ambiente**

1. En la esquina superior derecha, selecciona el dropdown de ambientes
2. Selecciona **"Entregas Agrupadas - Ambiente"**
3. Verifica que aparezca seleccionado

### **Paso 4: Configurar Autenticación** ⚠️ **IMPORTANTE**

1. Ve a **"Environments"** → **"Entregas Agrupadas - Ambiente"**
2. Busca la variable **`auth_token`**
3. En el campo **"Current Value"**, ingresa tu token de autenticación:
   ```
   Formato: base64(username:password)
   ```
4. **Para generar el token:**
   - Opción A: Usar herramienta online de base64
   - Opción B: Usar terminal: `echo -n "usuario:password" | base64`
   - Opción C: Usar JavaScript en Postman:
     ```javascript
     btoa("usuario:password")
     ```

---

## 📋 Endpoints Incluidos

### **1. Crear Entrega Agrupada** 🚛
- **Método:** POST
- **URL:** `/createDeliveryBooking`
- **Descripción:** Crea una entrega con múltiples pedidos
- **Características:**
  - Genera folios automáticamente
  - Valida datos de entrada
  - Crea bookings individuales
  - Asigna conductor y vehículo
  - Envía notificación push

### **2. Obtener Detalles de Entrega** 📊
- **Método:** GET
- **URL:** `/getGroupDeliveryDetails`
- **Descripción:** Consulta detalles de una entrega específica
- **Parámetros:** `folioEntrega`

### **3. Listar Entregas Agrupadas** 📋
- **Método:** GET
- **URL:** `/getGroupDeliveries`
- **Descripción:** Lista todas las entregas del sistema

### **4. Actualizar Estado de Pedido** ✅
- **Método:** POST
- **URL:** `/updatePedidoDeliveryStatus`
- **Descripción:** Actualiza estado de un pedido específico
- **Incluye:** Evidencia de entrega (fotos, firmas)

---

## 🔧 Variables de Entorno Preconfiguradas

| Variable | Descripción | Valor por Defecto |
|----------|-------------|-------------------|
| `base_url` | URL base de Firebase Functions | `https://us-central1-balle-813e3.cloudfunctions.net` |
| `auth_token` | Token de autenticación | **⚠️ DEBE CONFIGURARSE** |
| `folioEntrega` | Folio generado automáticamente | Se actualiza en cada request |
| `folioPedido1` | Folio del primer pedido | Se genera automáticamente |
| `folioPedido2` | Folio del segundo pedido | Se genera automáticamente |
| `lastFolioEntrega` | Último folio creado | Se guarda después de crear entrega |
| `test_customer_uid_1` | UID de cliente de prueba 1 | `customer_test_001` |
| `test_customer_uid_2` | UID de cliente de prueba 2 | `customer_test_002` |
| `test_driver_uid` | UID de conductor de prueba | `driver_test_001` |
| `test_vehicle_id` | ID de vehículo de prueba | `VEH-TEST-001` |

---

## 🧪 Scripts Automáticos Incluidos

### **Pre-request Scripts**
- **Generación automática de folios únicos**
- **Validación de variables de entorno**
- **Logs informativos en consola**

### **Test Scripts**
- **Validación de códigos de respuesta**
- **Verificación de estructura de datos**
- **Guardado automático de IDs para siguientes requests**
- **Logs de éxito/error en consola**

---

## 🎯 Flujo de Prueba Recomendado

### **1. Crear Entrega Agrupada**
```
POST /createDeliveryBooking
```
- Ejecuta el request principal
- Verifica que se generen los folios automáticamente
- Confirma respuesta exitosa (200)
- Observa que se guardan las variables para siguientes requests

### **2. Consultar Detalles**
```
GET /getGroupDeliveryDetails?folioEntrega={{lastFolioEntrega}}
```
- Usa automáticamente el folio de la entrega recién creada
- Verifica el estado y estructura de datos

### **3. Actualizar Estado de Pedido**
```
POST /updatePedidoDeliveryStatus
```
- Marca un pedido como entregado
- Incluye evidencia de entrega

### **4. Listar Todas las Entregas**
```
GET /getGroupDeliveries
```
- Verifica que aparezca la entrega creada en la lista

---

## 📊 Ejemplo de Datos de Prueba

La colección incluye datos de prueba realistas:

```json
{
  "folioEntrega": "ENT-1642680600000-001",
  "pedidos": [
    {
      "folioPedido": "PED-1642680600000-001",
      "cliente": {
        "nombre": "Juan Pérez García",
        "telefono": "+52555123456",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Av. Insurgentes Sur 123, Col. Roma Norte, CDMX",
        "lat": 19.4150,
        "lng": -99.1620
      },
      "productos": [
        {
          "nombre": "Laptop Dell Inspiron 15",
          "cantidad": 1,
          "precio": 15000.00
        }
      ]
    }
  ],
  "vehiculo": {
    "tipo": "Camioneta de Reparto",
    "placas": "ABC-1234"
  },
  "chofer": {
    "nombre": "Carlos Roberto Martínez",
    "telefono": "+52555777888"
  }
}
```

---

## ⚠️ Consideraciones Importantes

### **Autenticación**
- **OBLIGATORIO:** Configurar `auth_token` antes de usar
- Contactar al administrador para obtener credenciales
- El token debe estar en formato base64

### **Datos de Prueba**
- Los UIDs de clientes y conductores deben existir en la base de datos
- Los vehículos deben estar registrados si se usa un ID específico
- Los conductores deben estar disponibles (`queue: false`)

### **Respuestas de Error Comunes**
- **401:** Token de autenticación inválido
- **400:** Datos faltantes o conductor no disponible
- **500:** Error interno del servidor

---

## 🔍 Monitoreo y Debugging

### **Console Logs**
- Abre la **Postman Console** (View → Show Postman Console)
- Observa logs automáticos de cada request
- Verifica generación de folios y guardado de variables

### **Variables en Tiempo Real**
- Ve a **Environment** → **"Entregas Agrupadas - Ambiente"**
- Observa cómo se actualizan las variables automáticamente
- Verifica que `lastFolioEntrega` se guarde después de crear entregas

### **Tests Automáticos**
- Cada request incluye tests automáticos
- Verifica el panel de **"Test Results"** después de cada request
- Los tests validan estructura de respuesta y códigos de estado

---

## 🆘 Solución de Problemas

### **Error: "auth_token not configured"**
- Configurar la variable `auth_token` en el ambiente
- Verificar que el formato sea base64(username:password)

### **Error: "Conductor no encontrado"**
- Verificar que el UID del conductor exista en la base de datos
- Confirmar que el conductor esté disponible

### **Error: "Variables not updating"**
- Verificar que el ambiente esté seleccionado
- Revisar que los scripts pre-request se ejecuten correctamente

---

## 📞 Soporte

Para soporte adicional:
1. Revisar logs en Postman Console
2. Verificar variables de entorno
3. Contactar al equipo de desarrollo con:
   - Screenshots de errores
   - Logs de Postman Console
   - Datos de prueba utilizados

**¡La configuración de Postman está lista para usar! 🚀**
