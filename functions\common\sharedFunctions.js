const admin = require('firebase-admin');

// Funciones para entregas agrupadas
module.exports.generateDeliveryFolio = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ENT-${timestamp}-${random}`;
};

module.exports.optimizeRoute = async (pedidos) => {
    // Implementación básica de optimización de ruta
    // En producción, usar Google Maps Directions API con waypoints optimization
    const puntos = Object.values(pedidos).map((pedido, index) => ({
        orden: index + 1,
        pedidoId: pedido.folioPedido,
        lat: pedido.domicilio.lat,
        lng: pedido.domicilio.lng,
        direccion: pedido.domicilio.direccion,
        estimatedTime: `${10 + (index * 15)}:00` // Estimación simple
    }));

    return {
        puntos: puntos,
        distanciaTotal: puntos.length * 5.5, // Estimación simple
        tiempoEstimado: puntos.length * 20 // 20 min por parada
    };
};

module.exports.createGroupDelivery = async (deliveryData) => {
    const folioEntrega = module.exports.generateDeliveryFolio();
    const ruta = await module.exports.optimizeRoute(deliveryData.pedidos);

    const deliveryGroup = {
        folioEntrega: folioEntrega,
        fechaCreacion: new Date().toISOString(),
        vehiculo: deliveryData.vehiculo,
        chofer: deliveryData.chofer,
        status: 'PENDING', // PENDING, ASSIGNED, IN_PROGRESS, COMPLETED, CANCELLED
        pedidos: deliveryData.pedidos,
        ruta: ruta,
        totalPedidos: Object.keys(deliveryData.pedidos).length,
        pedidosEntregados: 0,
        observacionesGenerales: '',
        fechaAsignacion: null,
        fechaInicio: null,
        fechaFinalizacion: null
    };

    // Guardar en Firebase
    await admin.database().ref('deliveryGroups').child(folioEntrega).set(deliveryGroup);

    // Actualizar estado del conductor
    if (deliveryData.chofer && deliveryData.chofer.uid) {
        await admin.database().ref('users').child(deliveryData.chofer.uid).update({
            currentDeliveryGroup: folioEntrega,
            queue: true
        });
    }

    return { folioEntrega, deliveryGroup };
};

module.exports.updatePedidoStatus = async (folioEntrega, pedidoId, status, evidencia = {}, observaciones = '') => {
    const updates = {};
    updates[`deliveryGroups/${folioEntrega}/pedidos/${pedidoId}/statusEntrega`] = status;
    updates[`deliveryGroups/${folioEntrega}/pedidos/${pedidoId}/observaciones`] = observaciones;
    updates[`deliveryGroups/${folioEntrega}/pedidos/${pedidoId}/horaEntrega`] = new Date().toISOString();

    if (evidencia.foto) {
        updates[`deliveryGroups/${folioEntrega}/pedidos/${pedidoId}/evidenciaEntrega/foto`] = evidencia.foto;
    }
    if (evidencia.firma) {
        updates[`deliveryGroups/${folioEntrega}/pedidos/${pedidoId}/evidenciaEntrega/firma`] = evidencia.firma;
    }
    if (evidencia.recibidoPor) {
        updates[`deliveryGroups/${folioEntrega}/pedidos/${pedidoId}/evidenciaEntrega/recibidoPor`] = evidencia.recibidoPor;
    }

    await admin.database().ref().update(updates);

    // Actualizar contador de pedidos entregados
    const deliveryGroupRef = admin.database().ref(`deliveryGroups/${folioEntrega}`);
    const snapshot = await deliveryGroupRef.once('value');
    const deliveryGroup = snapshot.val();

    if (deliveryGroup) {
        const pedidosEntregados = Object.values(deliveryGroup.pedidos).filter(
            pedido => pedido.statusEntrega === 'DELIVERED'
        ).length;

        await deliveryGroupRef.update({
            pedidosEntregados: pedidosEntregados,
            status: pedidosEntregados === deliveryGroup.totalPedidos ? 'COMPLETED' : deliveryGroup.status
        });

        // Si se completaron todas las entregas, liberar al conductor
        if (pedidosEntregados === deliveryGroup.totalPedidos) {
            await admin.database().ref('users').child(deliveryGroup.chofer.uid).update({
                currentDeliveryGroup: null,
                queue: false
            });
        }
    }

    return true;
};

module.exports.UpdateBooking = (bookingData,order_id,transaction_id,gateway) => {
    if(bookingData.deliveryWithBid){
        let curChanges = {
            status: 'ACCEPTED',
            prepaid: true,
            transaction_id: transaction_id,
            gateway: gateway
        }
        Object.assign(curChanges, bookingData.paymentPacket);
    
        curChanges.driver = bookingData.selectedBid.driver;
        curChanges.driver_image =  bookingData.selectedBid.driver_image; 
        curChanges.driver_name = bookingData.selectedBid.driver_name;
        curChanges.driver_contact = bookingData.selectedBid.driver_contact;
        curChanges.driver_token = bookingData.selectedBid.driver_token;
        curChanges.vehicle_number = bookingData.selectedBid.vehicle_number;
        curChanges.vehicleModel = bookingData.selectedBid.vehicleModel;
        curChanges.vehicleMake = bookingData.selectedBid.vehicleMake;
        curChanges.driverRating = bookingData.selectedBid.driverRating;
        curChanges.trip_cost =  bookingData.selectedBid.trip_cost;
        curChanges.convenience_fees =  bookingData.selectedBid.convenience_fees;
        curChanges.driver_share =  bookingData.selectedBid.driver_share;
        curChanges.driverOffers = {};
        curChanges.requestedDrivers = {};
        curChanges.driverEstimates = {};
        curChanges.selectedBid = {};
        curChanges.fleetadmin = bookingData.selectedBid.fleetadmin ? bookingData.selectedBid.fleetadmin : null;
        curChanges.fleetCommission= bookingData.selectedBid.fleetadmin ? ((parseFloat( bookingData.selectedBid.trip_cost) - parseFloat(bookingData.selectedBid.convenience_fees)) * parseFloat(bookingData.fleet_admin_comission) / 100).toFixed(2):null;
    
        admin.database().ref('bookings').child(order_id).update(curChanges);
        admin.database().ref('users').child(curChanges.driver).update({queue:true});
    }else{
        let curChanges = {
            status: 'NEW',
            prepaid: true,
            transaction_id: transaction_id,
            gateway: gateway
        }
        Object.assign(curChanges, bookingData.paymentPacket);
        admin.database().ref('bookings').child(order_id).update(curChanges);
    }
}

module.exports.addEstimate = (bookingId, driverId, distance, deliveryWithBid) => {
    if(deliveryWithBid){
        const timein_text = ((distance * 2) + 1).toFixed(0) + ' min';
        admin.database().ref('bookings/' + bookingId + '/driverEstimates/' + driverId).set({distance: distance, timein_text :  timein_text});
    }
        return true;     
}

