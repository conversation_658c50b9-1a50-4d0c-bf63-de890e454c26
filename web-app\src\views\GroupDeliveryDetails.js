import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  <PERSON>rid,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Divider,
  IconButton,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import PersonIcon from "@mui/icons-material/Person";
import DirectionsCarIcon from "@mui/icons-material/DirectionsCar";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PendingIcon from "@mui/icons-material/Pending";
import { colors } from "../components/Theme/WebTheme";
import { FONT_FAMILY, SECONDORY_COLOR } from "../common/sharedFunctions";
import moment from "moment/min/moment-with-locales";
import CircularLoading from "../components/CircularLoading";
import AlertDialog from "../components/AlertDialog";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(3),
    fontFamily: FONT_FAMILY,
  },
  header: {
    display: "flex",
    alignItems: "center",
    marginBottom: theme.spacing(3),
  },
  backButton: {
    marginRight: theme.spacing(2),
  },
  title: {
    fontFamily: FONT_FAMILY,
    fontWeight: "bold",
    color: colors.BLACK,
  },
  card: {
    marginBottom: theme.spacing(2),
    borderRadius: "8px",
    boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
  },
  cardHeader: {
    backgroundColor: SECONDORY_COLOR,
    color: colors.WHITE,
  },
  statusChip: {
    fontWeight: "bold",
    color: colors.WHITE,
  },
  tableContainer: {
    marginTop: theme.spacing(2),
  },
  deliveryInfo: {
    display: "flex",
    alignItems: "center",
    marginBottom: theme.spacing(1),
  },
  icon: {
    marginRight: theme.spacing(1),
    color: SECONDORY_COLOR,
  },
}));

export default function GroupDeliveryDetails() {
  const { folioEntrega } = useParams();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const classes = useStyles();
  const dispatch = useDispatch();

  const [deliveryData, setDeliveryData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: "" });

  useEffect(() => {
    // Simular carga de datos por ahora
    // En una implementación real, aquí se haría la llamada a la API
    setTimeout(() => {
      setDeliveryData({
        folioEntrega: folioEntrega,
        fechaCreacion: new Date().toISOString(),
        status: "PENDING",
        chofer: {
          nombre: "Juan Pérez",
          telefono: "+52123456789",
          uid: "driver123"
        },
        vehiculo: {
          vehicleNumber: "ABC-123",
          vehicleModel: "Toyota Hiace",
          vehicleMake: "Toyota"
        },
        totalPedidos: 0,
        pedidosEntregados: 0,
        pedidos: {},
        observacionesGenerales: ""
      });
      setLoading(false);
    }, 1000);
  }, [folioEntrega]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'PENDING':
        return colors.YELLOW;
      case 'ASSIGNED':
        return colors.BLUE;
      case 'IN_PROGRESS':
        return colors.ORANGE;
      case 'COMPLETED':
        return colors.GREEN;
      case 'CANCELLED':
        return colors.RED;
      default:
        return colors.GRAY;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon />;
      default:
        return <PendingIcon />;
    }
  };

  if (loading) {
    return <CircularLoading />;
  }

  if (!deliveryData) {
    return (
      <div className={classes.root}>
        <Typography variant="h6" color="error">
          {t("delivery_not_found")}
        </Typography>
      </div>
    );
  }

  return (
    <div className={classes.root}>
      {/* Header */}
      <div className={classes.header}>
        <IconButton
          className={classes.backButton}
          onClick={() => navigate("/groupdeliveries")}
        >
          <ArrowBackIcon />
        </IconButton>
        <LocalShippingIcon className={classes.icon} />
        <Typography variant="h4" className={classes.title}>
          {t("delivery_details")} - {folioEntrega}
        </Typography>
      </div>

      <Grid container spacing={3}>
        {/* Información General */}
        <Grid item xs={12} md={6}>
          <Card className={classes.card}>
            <CardHeader
              className={classes.cardHeader}
              title={t("general_information")}
            />
            <CardContent>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("folio_entrega")}:</strong> {deliveryData.folioEntrega}
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("fecha_creacion")}:</strong>{" "}
                  {moment(deliveryData.fechaCreacion).format("lll")}
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("status")}:</strong>{" "}
                  <Chip
                    icon={getStatusIcon(deliveryData.status)}
                    label={t(deliveryData.status)}
                    className={classes.statusChip}
                    style={{ backgroundColor: getStatusColor(deliveryData.status) }}
                  />
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("total_pedidos")}:</strong> {deliveryData.totalPedidos}
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("pedidos_entregados")}:</strong> {deliveryData.pedidosEntregados}
                </Typography>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* Información del Conductor */}
        <Grid item xs={12} md={6}>
          <Card className={classes.card}>
            <CardHeader
              className={classes.cardHeader}
              title={
                <div style={{ display: "flex", alignItems: "center" }}>
                  <PersonIcon style={{ marginRight: 8 }} />
                  {t("driver_information")}
                </div>
              }
            />
            <CardContent>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("name")}:</strong> {deliveryData.chofer.nombre}
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("phone")}:</strong> {deliveryData.chofer.telefono}
                </Typography>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* Información del Vehículo */}
        <Grid item xs={12} md={6}>
          <Card className={classes.card}>
            <CardHeader
              className={classes.cardHeader}
              title={
                <div style={{ display: "flex", alignItems: "center" }}>
                  <DirectionsCarIcon style={{ marginRight: 8 }} />
                  {t("vehicle_information")}
                </div>
              }
            />
            <CardContent>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("vehicle_number")}:</strong> {deliveryData.vehiculo.vehicleNumber}
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("vehicle_model")}:</strong> {deliveryData.vehiculo.vehicleModel}
                </Typography>
              </div>
              <div className={classes.deliveryInfo}>
                <Typography variant="body1">
                  <strong>{t("vehicle_make")}:</strong> {deliveryData.vehiculo.vehicleMake}
                </Typography>
              </div>
            </CardContent>
          </Card>
        </Grid>

        {/* Lista de Pedidos */}
        <Grid item xs={12}>
          <Card className={classes.card}>
            <CardHeader
              className={classes.cardHeader}
              title={t("orders_list")}
            />
            <CardContent>
              {Object.keys(deliveryData.pedidos).length === 0 ? (
                <Typography variant="body1" color="textSecondary">
                  {t("no_orders_assigned")}
                </Typography>
              ) : (
                <TableContainer component={Paper} className={classes.tableContainer}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>{t("folio_pedido")}</TableCell>
                        <TableCell>{t("customer")}</TableCell>
                        <TableCell>{t("address")}</TableCell>
                        <TableCell>{t("total")}</TableCell>
                        <TableCell>{t("status")}</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.values(deliveryData.pedidos).map((pedido) => (
                        <TableRow key={pedido.folioPedido}>
                          <TableCell>{pedido.folioPedido}</TableCell>
                          <TableCell>{pedido.cliente?.nombre || "-"}</TableCell>
                          <TableCell>{pedido.domicilio?.direccion || "-"}</TableCell>
                          <TableCell>${pedido.total || 0}</TableCell>
                          <TableCell>
                            <Chip
                              label={t(pedido.statusEntrega || "PENDING")}
                              size="small"
                              style={{
                                backgroundColor: getStatusColor(pedido.statusEntrega || "PENDING"),
                                color: colors.WHITE,
                              }}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <AlertDialog open={commonAlert.open} onClose={() => setCommonAlert({ open: false, msg: "" })}>
        {commonAlert.msg}
      </AlertDialog>
    </div>
  );
}
