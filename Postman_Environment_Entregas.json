{"id": "12345678-1234-1234-1234-123456789abc", "name": "Entregas Agrupadas - Ambiente", "values": [{"key": "base_url", "value": "https://us-central1-balle-813e3.cloudfunctions.net", "type": "default", "enabled": true, "description": "URL base de las Firebase Functions"}, {"key": "auth_token", "value": "", "type": "secret", "enabled": true, "description": "Token de autenticación en formato base64(username:password). DEBE SER CONFIGURADO POR EL USUARIO."}, {"key": "folioEntrega", "value": "", "type": "default", "enabled": true, "description": "Folio de entrega generado automáticamente (se actualiza en cada request)"}, {"key": "folioPedido1", "value": "", "type": "default", "enabled": true, "description": "Folio del primer pedido (se genera automáticamente)"}, {"key": "folioPedido2", "value": "", "type": "default", "enabled": true, "description": "Folio del segundo pedido (se genera automáticamente)"}, {"key": "lastFolioEntrega", "value": "", "type": "default", "enabled": true, "description": "Último folio de entrega creado (se guarda automáticamente después de crear una entrega)"}, {"key": "lastBookingId", "value": "", "type": "default", "enabled": true, "description": "Último booking ID creado (se guarda automáticamente)"}, {"key": "test_customer_uid_1", "value": "customer_test_001", "type": "default", "enabled": true, "description": "UID de cliente de prueba 1"}, {"key": "test_customer_uid_2", "value": "customer_test_002", "type": "default", "enabled": true, "description": "UID de cliente de prueba 2"}, {"key": "test_driver_uid", "value": "driver_test_001", "type": "default", "enabled": true, "description": "UID de conductor de <PERSON>"}, {"key": "test_vehicle_id", "value": "VEH-TEST-001", "type": "default", "enabled": true, "description": "ID de vehículo de prueba"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-20T10:30:00.000Z", "_postman_exported_using": "Postman/10.0.0"}