import {
  FETCH_GROUP_DELIVERIES,
  FETCH_GROUP_DELIVERIES_SUCCESS,
  FETCH_GROUP_DELIVERIES_FAILED,
  CREATE_GROUP_DELIVERY,
  CREATE_GROUP_DELIVERY_SUCCESS,
  CREATE_GROUP_DELIVERY_FAILED,
  UPDATE_GROUP_DELIVERY,
  UPDATE_PEDIDO_STATUS,
  CLEAR_GROUP_DELIVERY
} from "../store/types";

const INITIAL_STATE = {
  groupDeliveries: [],
  loading: false,
  creating: false,
  updating: false,
  currentDelivery: null,
  error: {
    flag: false,
    msg: null
  }
};

export const groupdeliveryreducer = (state = INITIAL_STATE, action) => {
  switch (action.type) {
    case FETCH_GROUP_DELIVERIES:
      return {
        ...state,
        loading: true,
        error: {
          flag: false,
          msg: null
        }
      };

    case FETCH_GROUP_DELIVERIES_SUCCESS:
      return {
        ...state,
        groupDeliveries: action.payload,
        loading: false,
        error: {
          flag: false,
          msg: null
        }
      };

    case FETCH_GROUP_DELIVERIES_FAILED:
      return {
        ...state,
        groupDeliveries: [],
        loading: false,
        error: {
          flag: true,
          msg: action.payload
        }
      };

    case CREATE_GROUP_DELIVERY:
      return {
        ...state,
        creating: true,
        error: {
          flag: false,
          msg: null
        }
      };

    case CREATE_GROUP_DELIVERY_SUCCESS:
      return {
        ...state,
        creating: false,
        currentDelivery: action.payload,
        error: {
          flag: false,
          msg: null
        }
      };

    case CREATE_GROUP_DELIVERY_FAILED:
      return {
        ...state,
        creating: false,
        currentDelivery: null,
        error: {
          flag: true,
          msg: action.payload
        }
      };

    case UPDATE_GROUP_DELIVERY:
      return {
        ...state,
        updating: true,
        error: {
          flag: false,
          msg: null
        }
      };

    case UPDATE_PEDIDO_STATUS:
      return {
        ...state,
        updating: true,
        error: {
          flag: false,
          msg: null
        }
      };

    case CLEAR_GROUP_DELIVERY:
      return {
        ...state,
        currentDelivery: null,
        creating: false,
        updating: false,
        error: {
          flag: false,
          msg: null
        }
      };

    default:
      return state;
  }
};
