import {
  FETCH_GROUP_DELIVERIES,
  FETCH_GROUP_DELIVERIES_SUCCESS,
  FETCH_GROUP_DELIVERIES_FAILED,
  CREATE_GROUP_DELIVERY,
  CREATE_GROUP_DELIVERY_SUCCESS,
  CREATE_GROUP_DELIVERY_FAILED,
  UPDATE_GROUP_DELIVERY,
  UPDATE_PEDIDO_STATUS,
  CLEAR_GROUP_DELIVERY
} from "../store/types";
import { firebase } from '../config/configureFirebase';
import { RequestPushMsg } from '../other/NotificationFunctions';
import store from '../store/store';
import { get, onValue, update, off, push, set } from "firebase/database";

// Obtener entregas agrupadas
export const fetchGroupDeliveries = () => (dispatch) => {
  const { groupDeliveriesRef } = firebase;

  dispatch({
    type: FETCH_GROUP_DELIVERIES,
    payload: null,
  });

  onValue(groupDeliveriesRef, (snapshot) => {
    if (snapshot.val()) {
      const data = snapshot.val();
      const arr = Object.keys(data).map((i) => {
        data[i].id = i;
        return data[i];
      });
      dispatch({
        type: FETCH_GROUP_DELIVERIES_SUCCESS,
        payload: arr,
      });
    } else {
      dispatch({
        type: FETCH_GROUP_DELIVERIES_SUCCESS,
        payload: [],
      });
    }
  }, (error) => {
    dispatch({
      type: FETCH_GROUP_DELIVERIES_FAILED,
      payload: error.message,
    });
  });
};

// Crear nueva entrega agrupada
export const createGroupDelivery = (deliveryData) => async (dispatch) => {
  const { config } = firebase;

  dispatch({
    type: CREATE_GROUP_DELIVERY,
    payload: deliveryData,
  });

  try {
    const response = await fetch(`https://${config.projectId}.cloudfunctions.net/createGroupDelivery`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(config.projectId + ':' + store.getState().auth.accessToken)}`
      },
      body: JSON.stringify(deliveryData)
    });

    const result = await response.json();

    if (result.success) {
      dispatch({
        type: CREATE_GROUP_DELIVERY_SUCCESS,
        payload: result,
      });
      return result;
    } else {
      throw new Error(result.error || 'Error al crear entrega agrupada');
    }
  } catch (error) {
    dispatch({
      type: CREATE_GROUP_DELIVERY_FAILED,
      payload: error.message,
    });
    throw error;
  }
};

// Actualizar estado de entrega agrupada
export const updateGroupDeliveryStatus = (folioEntrega, status, observaciones) => async (dispatch) => {
  const { config } = firebase;

  dispatch({
    type: UPDATE_GROUP_DELIVERY,
    payload: { folioEntrega, status, observaciones },
  });

  try {
    const response = await fetch(`https://${config.projectId}.cloudfunctions.net/updateGroupDeliveryStatus`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(config.projectId + ':' + store.getState().auth.accessToken)}`
      },
      body: JSON.stringify({ folioEntrega, status, observaciones })
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Error al actualizar estado de entrega');
    }

    return result;
  } catch (error) {
    console.error('Error updating group delivery status:', error);
    throw error;
  }
};

// Actualizar estado de pedido específico
export const updatePedidoDeliveryStatus = (folioEntrega, pedidoId, status, evidencia, observaciones) => async (dispatch) => {
  const { config } = firebase;

  dispatch({
    type: UPDATE_PEDIDO_STATUS,
    payload: { folioEntrega, pedidoId, status, evidencia, observaciones },
  });

  try {
    const response = await fetch(`https://${config.projectId}.cloudfunctions.net/updatePedidoDeliveryStatus`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(config.projectId + ':' + store.getState().auth.accessToken)}`
      },
      body: JSON.stringify({ folioEntrega, pedidoId, status, evidencia, observaciones })
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Error al actualizar estado del pedido');
    }

    return result;
  } catch (error) {
    console.error('Error updating pedido status:', error);
    throw error;
  }
};

// Obtener detalles de entrega específica
export const getGroupDeliveryDetails = (folioEntrega) => async (dispatch) => {
  const { config } = firebase;

  try {
    const response = await fetch(`https://${config.projectId}.cloudfunctions.net/getGroupDeliveryDetails?folioEntrega=${folioEntrega}`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${btoa(config.projectId + ':' + store.getState().auth.accessToken)}`
      }
    });

    const result = await response.json();

    if (result.success) {
      return result.delivery;
    } else {
      throw new Error(result.error || 'Error al obtener detalles de entrega');
    }
  } catch (error) {
    console.error('Error getting group delivery details:', error);
    throw error;
  }
};

// Obtener reporte de entregas agrupadas
export const getGroupDeliveryReport = (filters = {}) => async (dispatch) => {
  const { config } = firebase;

  try {
    const queryParams = new URLSearchParams(filters).toString();
    const response = await fetch(`https://${config.projectId}.cloudfunctions.net/getGroupDeliveryReport?${queryParams}`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${btoa(config.projectId + ':' + store.getState().auth.accessToken)}`
      }
    });

    const result = await response.json();

    if (result.success) {
      return result;
    } else {
      throw new Error(result.error || 'Error al obtener reporte');
    }
  } catch (error) {
    console.error('Error getting group delivery report:', error);
    throw error;
  }
};

// Limpiar estado de entrega agrupada
export const clearGroupDelivery = () => (dispatch) => {
  dispatch({
    type: CLEAR_GROUP_DELIVERY,
    payload: null,
  });
};

// Detener escucha de entregas agrupadas
export const stopGroupDeliveriesFetch = () => (dispatch) => {
  const { groupDeliveriesRef } = firebase;
  off(groupDeliveriesRef);
};

// Generar folio de entrega
export const generateDeliveryFolio = () => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ENT-${timestamp}-${random}`;
};

// Optimizar ruta (función auxiliar)
export const optimizeRoute = (pedidos) => {
  // Implementación básica de optimización de ruta
  const puntos = Object.values(pedidos).map((pedido, index) => ({
    orden: index + 1,
    pedidoId: pedido.folioPedido,
    lat: pedido.domicilio.lat,
    lng: pedido.domicilio.lng,
    direccion: pedido.domicilio.direccion,
    estimatedTime: `${10 + (index * 15)}:00`
  }));
  
  return {
    puntos: puntos,
    distanciaTotal: puntos.length * 5.5,
    tiempoEstimado: puntos.length * 20
  };
};
