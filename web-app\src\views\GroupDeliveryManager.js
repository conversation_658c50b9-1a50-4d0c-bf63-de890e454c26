import React, { useState, useEffect, useRef } from "react";
import MaterialTable from "material-table";
import { useSelector, useDispatch } from "react-redux";
import CircularLoading from "../components/CircularLoading";
import { api } from "common";
import { makeStyles } from "@mui/styles";
import { useTranslation } from "react-i18next";
import { Modal, Grid, Typography } from "@mui/material";
import Button from "components/CustomButtons/Button.js";
import CancelIcon from "@mui/icons-material/Cancel";
import AlertDialog from "../components/AlertDialog";
import CircularProgress from "@mui/material/CircularProgress";
import Tooltip from "@mui/material/Tooltip";
import { colors } from "../components/Theme/WebTheme";
import moment from "moment/min/moment-with-locales";
import { useNavigate } from "react-router-dom";
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import {FONT_FAMILY, SECONDORY_COLOR } from "../common/sharedFunctions";
import { ThemeProvider } from '@mui/material/styles';
import theme from "styles/tableStyle";

const useStyles = makeStyles((theme) => ({
  modal: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  submit3: {
    width: "100%",
    borderRadius: 3,
    marginTop: 2,
    padding: 4,
  },
  paper: {
    width: 500,
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
    borderRadius: 15,
    alignItems: "center",
    justifyContent: "center",
  },
}));

export default function GroupDeliveryManager() {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const settings = useSelector((state) => state.settingsdata.settings);
  const auth = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const classes = useStyles();
  const rootRef = useRef();

  const [data, setData] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [loading, setLoading] = useState(true);
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: "" });

  useEffect(() => {
    // Simular datos por ahora hasta que se implemente la API
    setData([]);
    setLoading(false);
  }, [dispatch]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'PENDING':
        return colors.YELLOW;
      case 'ASSIGNED':
        return colors.BLUE;
      case 'IN_PROGRESS':
        return colors.ORANGE;
      case 'COMPLETED':
        return colors.GREEN;
      case 'CANCELLED':
        return colors.RED;
      default:
        return colors.GRAY;
    }
  };

  const columns = [
    {
      title: t("folio_entrega"),
      field: "folioEntrega",
      editable: "never",
      cellStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
      headerStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
    },
    {
      title: t("fecha_creacion"),
      field: "fechaCreacion",
      editable: "never",
      render: (rowData) =>
        rowData.fechaCreacion
          ? moment(rowData.fechaCreacion).format("lll")
          : "",
      cellStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
      headerStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
    },
    {
      title: t("chofer"),
      field: "chofer.nombre",
      editable: "never",
      cellStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
      headerStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
    },
    {
      title: t("vehiculo"),
      field: "vehiculo.vehicleNumber",
      editable: "never",
      cellStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
      headerStyle: {
        textAlign: isRTL === "rtl" ? "right" : "left",
      },
    },
    {
      title: t("total_pedidos"),
      field: "totalPedidos",
      editable: "never",
      type: "numeric",
      cellStyle: {
        textAlign: "center",
      },
      headerStyle: {
        textAlign: "center",
      },
    },
    {
      title: t("pedidos_entregados"),
      field: "pedidosEntregados",
      editable: "never",
      type: "numeric",
      cellStyle: {
        textAlign: "center",
      },
      headerStyle: {
        textAlign: "center",
      },
    },
    {
      title: t("status"),
      field: "status",
      editable: "never",
      render: (rowData) => (
        <div
          style={{
            backgroundColor: getStatusColor(rowData.status),
            color: colors.WHITE,
            padding: "4px 8px",
            borderRadius: "4px",
            textAlign: "center",
            fontSize: "0.8em",
            fontWeight: "bold",
          }}
        >
          {t(rowData.status)}
        </div>
      ),
      cellStyle: {
        textAlign: "center",
      },
      headerStyle: {
        textAlign: "center",
      },
    },
  ];

  return loading ? (
    <CircularLoading />
  ) : (
    <div ref={rootRef}>
      <ThemeProvider theme={theme}>
        <MaterialTable
          title={
            <div style={{ display: "flex", alignItems: "center" }}>
              <LocalShippingIcon style={{ marginRight: 8 }} />
              {t("group_deliveries_title")}
            </div>
          }
          columns={columns}
          style={{
            direction: isRTL === "rtl" ? "rtl" : "ltr",
            borderRadius: "8px",
            boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
            padding: "20px",
          }}
          data={data}
          onRowClick={(evt, selectedRow) =>
            setSelectedRow(selectedRow.tableData.id)
          }
          options={{
            pageSize: 10,
            pageSizeOptions: [10, 15, 20],
            exportButton: true,
            rowStyle: (rowData) => ({
              backgroundColor:
                selectedRow === rowData.tableData.id ? colors.ROW_SELECTED : colors.WHITE
            }),
            editable: {
              backgroundColor: colors.Header_Text,
              fontSize: "0.8em",
              fontWeight: "bold ",     
            },
            headerStyle: {
              position: "sticky",
              top: "0px",
              fontSize: "0.8em",
              fontWeight: "bold ",
              color: colors.BLACK,
              backgroundColor: SECONDORY_COLOR,
              textAlign: "center",
              border: `1px solid ${colors.TABLE_BORDER}`,
            },
            cellStyle: {
              border: `1px solid ${colors.TABLE_BORDER}`,
              textAlign: "center",
            },
            actionsColumnIndex: -1,
          }}
          actions={[
            {
              icon: 'add',
              tooltip: t("create_group_delivery"),
              isFreeAction: true,
              onClick: (event) => navigate(`/groupdeliveries/create`)
            },
            (rowData) => ({
              tooltip: t("view_details"),
              icon: () => (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    flexWrap: "wrap",
                    marginRight: 0
                  }}
                >
                  <VisibilityIcon />  
                </div>
              ),
              onClick: (event, rowData) => {
                navigate(`/groupdeliveries/${rowData.folioEntrega}`)
              }
            }),
          ]}
        />
      </ThemeProvider>
      <AlertDialog open={commonAlert.open} onClose={() => setCommonAlert({ open: false, msg: "" })}>
        {commonAlert.msg}
      </AlertDialog>
    </div>
  );
}
