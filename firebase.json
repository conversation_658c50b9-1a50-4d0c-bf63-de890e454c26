{"database": {"rules": "./json/database-rules.json"}, "storage": {"rules": "./json/storage.rules"}, "hosting": {"public": "web-app/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/", "destination": "/index.html"}, {"source": "/dashboard", "destination": "/index.html"}, {"source": "/bookings", "destination": "/index.html"}, {"source": "/addbookings", "destination": "/index.html"}, {"source": "/profile", "destination": "/index.html"}, {"source": "/cartypes", "destination": "/index.html"}, {"source": "/cancelreasons", "destination": "/index.html"}, {"source": "/promos", "destination": "/index.html"}, {"source": "/users/**", "destination": "/index.html"}, {"source": "/notifications", "destination": "/index.html"}, {"source": "/userwallet", "destination": "/index.html"}, {"source": "/addtowallet", "destination": "/index.html"}, {"source": "/settings", "destination": "/index.html"}, {"source": "/allreports", "destination": "/index.html"}, {"source": "/withdraws", "destination": "/index.html"}, {"source": "/about-us", "destination": "/index.html"}, {"source": "/contact-us", "destination": "/index.html"}, {"source": "/privacy-policy", "destination": "/index.html"}, {"source": "/term-condition", "destination": "/index.html"}, {"source": "/cars", "destination": "/index.html"}, {"source": "/login", "destination": "/index.html"}, {"source": "/get_providers", "function": "get_providers"}, {"source": "/send_notification", "function": "send_notification"}, {"source": "/check_user_exists", "function": "check_user_exists"}, {"source": "/validate_referrer", "function": "validate_referrer"}, {"source": "/user_signup", "function": "user_signup"}, {"source": "/success", "function": "success"}, {"source": "/cancel", "function": "cancel"}, {"source": "/generatePermanentAdminToken", "function": "generatePermanentAdminToken"}, {"source": "/braintree-link", "function": "braintree-link"}, {"source": "/braintree-process", "function": "braintree-process"}, {"source": "/culqi-link", "function": "culqi-link"}, {"source": "/culqi-process", "function": "culqi-process"}, {"source": "/flutterwave-link", "function": "flutterwave-link"}, {"source": "/flutterwave-process", "function": "flutterwave-process"}, {"source": "/liqpay-link", "function": "liqpay-link"}, {"source": "/liqpay-process", "function": "liqpay-process"}, {"source": "/payfast-link", "function": "payfast-link"}, {"source": "/payfast-process", "function": "payfast-process"}, {"source": "/paypal-link", "function": "paypal-link"}, {"source": "/paypal-process", "function": "paypal-process"}, {"source": "/paystack-link", "function": "paystack-link"}, {"source": "/paystack-process", "function": "paystack-process"}, {"source": "/paytm-link", "function": "paytm-link"}, {"source": "/paytm-process", "function": "paytm-process"}, {"source": "/payulatam-link", "function": "payulatam-link"}, {"source": "/payulatam-process", "function": "payulatam-process"}, {"source": "/securepay-link", "function": "securepay-link"}, {"source": "/securepay-process", "function": "securepay-process"}, {"source": "/stripe-link", "function": "stripe-link"}, {"source": "/stripe-process", "function": "stripe-process"}, {"source": "/mercadopago-link", "function": "mercadopago-link"}, {"source": "/mercadopago-process", "function": "mercadopago-process"}, {"source": "/googleapi", "function": "googlea<PERSON>"}, {"source": "/test-link", "function": "test-link"}, {"source": "/test-process", "function": "test-process"}, {"source": "/squareup-link", "function": "squareup-link"}, {"source": "/squareup-process", "function": "squareup-process"}, {"source": "/squareup-addcard", "function": "squareup-addcard"}, {"source": "/wipay-link", "function": "wipay-link"}, {"source": "/wipay-process", "function": "wipay-process"}, {"source": "/razorpay-link", "function": "razorpay-link"}, {"source": "/razorpay-process", "function": "razorpay-process"}, {"source": "/paymongo-link", "function": "paymongo-link"}, {"source": "/paymongo-process", "function": "paymongo-process"}, {"source": "/iyzico-link", "function": "iyzico-link"}, {"source": "/iyzico-process", "function": "iyzico-process"}, {"source": "/slickpay-link", "function": "slickpay-link"}, {"source": "/slickpay-process", "function": "slickpay-process"}, {"source": "/getservertime", "function": "getservertime"}, {"source": "/update_user_email", "function": "update_user_email"}, {"source": "/checksmtpdetails", "function": "checksmtpdetails"}, {"source": "/request_mobile_otp", "function": "request_mobile_otp"}, {"source": "/verify_mobile_otp", "function": "verify_mobile_otp"}, {"source": "/update_auth_mobile", "function": "update_auth_mobile"}, {"source": "/check_auth_exists", "function": "check_auth_exists"}, {"source": "/createDeliveryBooking", "function": "createDeliveryBooking"}, {"source": "/createDeliveryBookingTest", "function": "createDeliveryBookingTest"}, {"source": "/sos", "destination": "/index.html"}, {"source": "/bookings/bookingdetails/**", "destination": "/index.html"}, {"source": "/users/customerdetails/**", "destination": "/index.html"}, {"source": "/users/driverdetails/**", "destination": "/index.html"}, {"source": "/users/userdocuments/**/**", "destination": "/index.html"}, {"source": "/users/fleetadminupdate/**", "destination": "/index.html"}, {"source": "/users/adminupdate/**", "destination": "/index.html"}, {"source": "/users/addrider", "destination": "/index.html"}, {"source": "/users/adddriver", "destination": "/index.html"}, {"source": "/users/addfleetadmin", "destination": "/index.html"}, {"source": "/users/addadmin", "destination": "/index.html"}, {"source": "/notifications/addnotifications", "destination": "/index.html"}, {"source": "/cars/editcar", "destination": "/index.html"}, {"source": "/cars/editcar/**", "destination": "/index.html"}, {"source": "/cartypes/addcartype", "destination": "/index.html"}, {"source": "/cartypes/updatecartypes/**", "destination": "/index.html"}, {"source": "/promos/editpromo", "destination": "/index.html"}, {"source": "/promos/editpromo/**", "destination": "/index.html"}, {"source": "/cartypes/editcartype", "destination": "/index.html"}, {"source": "/cartypes/editcartype/**", "destination": "/index.html"}, {"source": "/users/edituser/**", "destination": "/index.html"}, {"source": "/users/edituser/**/**", "destination": "/index.html"}, {"source": "/*", "destination": "/index.html"}]}, "functions": {"predeploy": []}}