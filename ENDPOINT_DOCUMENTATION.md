# 📦 Documentación del Endpoint createDeliveryBooking

## 🎯 Resumen

El endpoint `createDeliveryBooking` permite crear entregas agrupadas donde un conductor puede entregar múltiples pedidos de diferentes clientes en una sola ruta optimizada.

## 🔗 URL del Endpoint

```
POST https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBooking
```

## 🔑 Autenticación

**Requerida:** Autenticación básica HTTP
```
Authorization: Basic [base64(username:password)]
```

## 📋 Estructura de Datos

### Request Body (JSON)

```json
{
  "folioEntrega": "ENT-1234567890-001",
  "pedidos": [
    {
      "folioPedido": "PED-001",
      "cliente": {
        "uid": "customer_123",
        "nombre": "<PERSON>",
        "telefono": "+52123456789",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Calle Principal 123",
        "lat": 19.4326,
        "lng": -99.1332,
        "referencias": "Casa azul"
      },
      "productos": [
        {
          "id": "PROD-001",
          "nombre": "Producto A",
          "cantidad": 2,
          "precio": 100.00
        }
      ],
      "total": 200.00,
      "instrucciones": "Entregar en recepción",
      "observaciones": "Cliente prefiere mañana"
    }
  ],
  "vehiculo": {
    "id": "VEH-001",
    "tipo": "Camión",
    "placas": "ABC-123",
    "modelo": "Ford Transit",
    "marca": "Ford"
  },
  "chofer": {
    "uid": "driver_456",
    "nombre": "Carlos López",
    "telefono": "+52987654321",
    "pushToken": "firebase_token_123"
  },
  "observacionesGenerales": "Entrega urgente"
}
```

### Campos Requeridos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| `folioEntrega` | String | Identificador único de la entrega agrupada |
| `pedidos` | Array | Lista de pedidos a entregar (mínimo 1) |
| `vehiculo` | Object | Información del vehículo asignado |
| `chofer` | Object | Información del conductor asignado |

### Campos del Pedido

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `folioPedido` | String | ✅ | Folio único del pedido |
| `cliente` | Object | ✅ | Información del cliente |
| `domicilio` | Object | ✅ | Dirección de entrega |
| `productos` | Array | ❌ | Lista de productos |
| `total` | Number | ❌ | Total del pedido |
| `instrucciones` | String | ❌ | Instrucciones de entrega |
| `observaciones` | String | ❌ | Observaciones adicionales |

## 📤 Respuesta Exitosa

```json
{
  "success": true,
  "message": "Entrega agrupada creada exitosamente",
  "folioEntrega": "ENT-1234567890-001",
  "totalPedidos": 3,
  "bookingsCreados": [
    {
      "bookingId": "booking_123",
      "folioPedido": "PED-001",
      "reference": "ABCDEF"
    }
  ],
  "choferAsignado": {
    "uid": "driver_456",
    "nombre": "Carlos López"
  },
  "vehiculoAsignado": {
    "id": "VEH-001",
    "tipo": "Camión",
    "placas": "ABC-123"
  }
}
```

## ❌ Respuestas de Error

### 401 - No Autorizado
```json
{
  "error": "Unauthorized api call"
}
```

### 400 - Datos Inválidos
```json
{
  "error": "Faltan campos requeridos: folioEntrega, pedidos, vehiculo, chofer"
}
```

### 400 - Conductor No Disponible
```json
{
  "error": "El conductor ya tiene una entrega asignada"
}
```

### 500 - Error del Servidor
```json
{
  "error": "Error interno del servidor",
  "details": "Descripción del error"
}
```

## 🔄 Flujo de Funcionamiento

1. **Validación de Autenticación**
   - Verifica credenciales del usuario

2. **Validación de Datos**
   - Campos requeridos presentes
   - Conductor existe y está disponible
   - Vehículo existe (si se proporciona ID)

3. **Creación de Bookings**
   - Genera booking individual para cada pedido
   - Asigna referencias únicas y OTPs

4. **Creación del Grupo de Entrega**
   - Optimiza ruta de entrega
   - Guarda en `/deliveryGroups/{folioEntrega}`

5. **Actualización del Conductor**
   - Marca como ocupado (`queue: true`)
   - Asigna grupo de entrega actual

6. **Notificación**
   - Envía push notification al conductor

## 🗄️ Estructura en Base de Datos

### Booking Individual (`/bookings/{bookingId}`)
```json
{
  "reference": "ABCDEF",
  "status": "NEW",
  "delivery_type": "GROUP_DELIVERY",
  "folioEntrega": "ENT-1234567890-001",
  "folioPedido": "PED-001",
  "customer": "customer_123",
  "driver": "driver_456",
  "vehicle_number": "ABC-123",
  "trip_cost": 200.00,
  "payment_mode": "cash"
}
```

### Grupo de Entrega (`/deliveryGroups/{folioEntrega}`)
```json
{
  "folioEntrega": "ENT-1234567890-001",
  "status": "PENDING",
  "totalPedidos": 3,
  "pedidosEntregados": 0,
  "chofer": { "uid": "driver_456", "nombre": "Carlos López" },
  "vehiculo": { "placas": "ABC-123", "tipo": "Camión" },
  "ruta": {
    "puntos": [...],
    "distanciaTotal": 15.5,
    "tiempoEstimado": 60
  }
}
```

## 🎯 Estados del Sistema

### Estados de Booking
- `NEW` → `ACCEPTED` → `REACHED` → `PENDING` → `PAID` → `COMPLETE`

### Estados de Grupo de Entrega
- `PENDING` → `ASSIGNED` → `IN_PROGRESS` → `COMPLETED` / `CANCELLED`

### Estados de Pedido Individual
- `PENDING` → `DELIVERED` / `FAILED`

## 🔧 Funciones Auxiliares

### `generateDeliveryFolio()`
Genera folios únicos: `ENT-{timestamp}-{random}`

### `optimizeRoute(pedidos)`
Optimiza la ruta de entrega (implementación básica)

### `updatePedidoStatus(folioEntrega, pedidoId, status, evidencia)`
Actualiza estado de pedidos individuales

## 📱 Notificaciones Push

Se envía automáticamente al conductor:
```json
{
  "title": "Nueva Entrega",
  "msg": "Nueva entrega agrupada asignada: ENT-1234567890-001",
  "screen": "GroupDelivery",
  "params": { "folioEntrega": "ENT-1234567890-001" }
}
```

## 🧪 Pruebas

Ejecutar el script de prueba:
```bash
node test-endpoint-delivery.js
```

Para ver la documentación completa:
```bash
node test-endpoint-delivery.js --help
```

## ⚠️ Consideraciones Importantes

1. **Autenticación**: Contactar al administrador para credenciales
2. **Conductores**: Deben existir en la base de datos y estar disponibles
3. **Vehículos**: Si se proporciona ID, debe existir en `/cars/{id}`
4. **Límites**: Verificar cuotas de Firebase Functions
5. **Optimización**: La ruta actual es básica, considerar Google Maps API

## 🔗 URLs Relacionadas

- **Endpoint Principal**: `/createDeliveryBooking`
- **Actualizar Estado**: `/updatePedidoDeliveryStatus`
- **Obtener Detalles**: `/getGroupDeliveryDetails`
- **Listar Entregas**: `/getGroupDeliveries`
- **Reportes**: `/getGroupDeliveryReport`
