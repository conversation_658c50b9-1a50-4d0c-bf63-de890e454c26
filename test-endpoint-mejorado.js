// 🚀 Script de prueba MEJORADO para el endpoint createDeliveryBookingTest
// Ejecutar con: node test-endpoint-mejorado.js

const https = require('https');

// 🔗 URL del endpoint (usando tu configuración)
const ENDPOINT_URL = 'https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBookingTest';

// 📦 Datos de prueba mejorados basados en tu estructura actual
const testDataMejorado = {
  "folioEntrega": "ENT-MEJORADO-" + Date.now(),
  "origen": {
    "direccion": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jal.",
    "lat": 20.6858704,
    "lng": -103.3154975,
    "referencias": "Almacén principal - Edificio corporativo",
    "contacto": {
      "nombre": "Supervisor de Almacén",
      "telefono": "+************"
    }
  },
  "pedidos": [
    {
      "folioPedido": "PED-MEJORADO-001",
      "cliente": {
        "uid": "VeSkwck41kXpdJIoDQH4sMjYror1",
        "nombre": "Jonathan <PERSON>esteros",
        "telefono": "+************",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "C. Álvarez del Castillo 760, Blanco y Cuéllar, 44370 Guadalajara, Jal.",
        "referencias": "Edificio blanco con portón café, timbre 3A"
      },
      "productos": [
        {
          "id": "PROD-TEST-001",
          "nombre": "Laptop Dell Inspiron (PRUEBA)",
          "cantidad": 1,
          "precio": 15000.00,
          "peso": 2.5,
          "dimensiones": "35x25x2 cm",
          "categoria": "ELECTRONICA",
          "fragil": true
        },
        {
          "id": "PROD-TEST-002",
          "nombre": "Mouse inalámbrico (PRUEBA)",
          "cantidad": 1,
          "precio": 500.00,
          "peso": 0.1,
          "dimensiones": "10x6x3 cm",
          "categoria": "ACCESORIOS",
          "fragil": false
        }
      ],
      "total": 15500.00,
      "instrucciones": "Entregar en recepción del edificio - MODO PRUEBA",
      "observaciones": "Cliente de prueba - disponible 24/7",
      "prioridad": "ALTA",
      "ventanaEntrega": {
        "inicio": "09:00",
        "fin": "18:00",
        "fecha": "2024-01-15"
      },
      "tipoEntrega": "DOMICILIO",
      "requiereEvidencia": true,
      "requiereFirma": true,
      "metodoPago": "EFECTIVO",
      "montoACobrar": 15500.00,
      "estado": "PENDIENTE"
    },
    {
      "folioPedido": "PED-MEJORADO-002",
      "cliente": {
        "uid": "saWRbrfvJRawpH3ccXCvAhdCbAu2",
        "nombre": "María González",
        "telefono": "+52555987654",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "C. Industria 1512, San Juan Bosco, 44730 Guadalajara, Jal.",
        "referencias": "Local comercial con letrero azul"
      },
      "productos": [
        {
          "id": "PROD-TEST-003",
          "nombre": "Impresora HP LaserJet (PRUEBA)",
          "cantidad": 1,
          "precio": 3500.00,
          "peso": 8.5,
          "dimensiones": "40x30x25 cm",
          "categoria": "ELECTRONICA",
          "fragil": true
        }
      ],
      "total": 3500.00,
      "instrucciones": "Tocar timbre y preguntar por María - MODO PRUEBA",
      "observaciones": "Negocio de prueba - abierto 24/7",
      "prioridad": "MEDIA",
      "ventanaEntrega": {
        "inicio": "10:00",
        "fin": "16:00",
        "fecha": "2024-01-15"
      },
      "tipoEntrega": "COMERCIAL",
      "requiereEvidencia": true,
      "requiereFirma": false,
      "metodoPago": "TARJETA",
      "montoACobrar": 3500.00,
      "estado": "PENDIENTE"
    }
  ],
  "vehiculo": {
    "id": "-OVUvzOLck6hU7Ppgfkb",
    "tipo": "CAMIONETA",
    "placas": "Eribdc34",
    "modelo": "Ram 2500",
    "marca": "Ford",
    "capacidad": "1500kg",
    "capacidadVolumen": "10m³",
    "combustible": "GASOLINA",
    "estado": "DISPONIBLE",
    "gps": true,
    "refrigeracion": false
  },
  "chofer": {
    "uid": "test_driver_001",
    "nombre": "Diego Ballesteros",
    "telefono": "+************",
    "pushToken": "test_firebase_token",
    "licencia": "ABC123456",
    "experiencia": "5 años",
    "calificacion": 4.8,
    "estado": "DISPONIBLE",
    "ubicacionActual": {
      "lat": 20.6858704,
      "lng": -103.3154975
    }
  },
  "configuracionEntrega": {
    "optimizarRuta": true,
    "permitirReordenamiento": true,
    "notificarClientes": true,
    "requiereConfirmacion": false,
    "tiempoMaximoEntrega": 480,
    "distanciaMaxima": 50,
    "algoritmoOptimizacion": "GOOGLE_MAPS",
    "evitarPeajes": false,
    "evitarAutopistas": false
  },
  "restricciones": {
    "horaInicioOperacion": "08:00",
    "horaFinOperacion": "20:00",
    "diasOperacion": ["LUNES", "MARTES", "MIERCOLES", "JUEVES", "VIERNES", "SABADO"],
    "zonaOperacion": "GUADALAJARA_METROPOLITANA",
    "pesoMaximo": 1500,
    "volumenMaximo": 10
  },
  "notificaciones": {
    "notificarInicio": true,
    "notificarLlegada": true,
    "notificarEntrega": true,
    "notificarFinalizacion": true,
    "incluirUbicacion": true,
    "incluirFoto": true
  },
  "metadatos": {
    "creadoPor": "sistema_pruebas",
    "canal": "WEB_ADMIN",
    "version": "2.0",
    "entorno": "TEST",
    "fechaCreacion": new Date().toISOString(),
    "sistemaOrigen": "ERP_COMERCIAL",
    "correlacionId": "CORR-" + Date.now()
  },
  "observacionesGenerales": "🧪 ENTREGA DE PRUEBA MEJORADA - Ruta optimizada para zona centro de Guadalajara. Incluye campos adicionales para mejor gestión."
};

// 🧪 Función para hacer la petición HTTP
function testEndpointMejorado() {
  const postData = JSON.stringify(testDataMejorado);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'User-Agent': 'Test-Script-Mejorado/1.0'
    }
  };

  console.log('🚀 Iniciando prueba MEJORADA del endpoint createDeliveryBookingTest...');
  console.log('📍 URL:', ENDPOINT_URL);
  console.log('📦 Folio de entrega:', testDataMejorado.folioEntrega);
  console.log('🎯 Total de pedidos:', testDataMejorado.pedidos.length);
  console.log('🚛 Conductor:', testDataMejorado.chofer.nombre);
  console.log('🚗 Vehículo:', `${testDataMejorado.vehiculo.marca} ${testDataMejorado.vehiculo.modelo} (${testDataMejorado.vehiculo.placas})`);
  console.log('\n⏳ Enviando petición...\n');

  const req = https.request(ENDPOINT_URL, options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📊 Código de respuesta:', res.statusCode);
      console.log('📋 Headers de respuesta:', JSON.stringify(res.headers, null, 2));
      
      try {
        const response = JSON.parse(data);
        console.log('\n✅ Respuesta del servidor:');
        console.log(JSON.stringify(response, null, 2));
        
        if (res.statusCode === 200 && response.success) {
          console.log('\n🎉 ¡Prueba MEJORADA exitosa!');
          console.log('📝 Folio de entrega creado:', response.folioEntrega);
          console.log('📊 Total de pedidos:', response.totalPedidos);
          console.log('🚛 Conductor asignado:', response.choferAsignado?.nombre || 'N/A');
          console.log('🚗 Vehículo asignado:', response.vehiculoAsignado?.placas || 'N/A');
          
          if (response.rutaOptimizada) {
            console.log('🗺️ Ruta optimizada:', response.rutaOptimizada.distanciaTotal + ' km');
            console.log('⏱️ Tiempo estimado:', response.rutaOptimizada.tiempoEstimado + ' min');
          }
          
          if (response.bookingsCreados) {
            console.log('📋 Bookings creados:');
            response.bookingsCreados.forEach((booking, index) => {
              console.log(`   ${index + 1}. ${booking.folioPedido} -> ${booking.bookingId}`);
            });
          }
        } else {
          console.log('\n❌ Error en la prueba');
          console.log('💬 Mensaje:', response.error || response.message);
        }
      } catch (error) {
        console.log('\n❌ Error al parsear la respuesta JSON:');
        console.log('📄 Respuesta cruda:', data);
        console.log('🐛 Error:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ Error en la petición:');
    console.log('🐛 Error:', error.message);
  });

  req.write(postData);
  req.end();
}

// 📋 Función para mostrar las mejoras incluidas
function mostrarMejoras() {
  console.log(`
🔧 MEJORAS INCLUIDAS EN ESTE BODY:

📍 ORIGEN MEJORADO:
- ✅ Información de contacto del almacén
- ✅ Referencias detalladas del punto de origen

📦 PRODUCTOS MEJORADOS:
- ✅ Peso y dimensiones de cada producto
- ✅ Categoría del producto
- ✅ Indicador de fragilidad

🎯 PEDIDOS MEJORADOS:
- ✅ Prioridad de entrega (ALTA, MEDIA, BAJA)
- ✅ Ventana de entrega con horarios específicos
- ✅ Tipo de entrega (DOMICILIO, COMERCIAL)
- ✅ Requerimientos de evidencia y firma
- ✅ Método de pago y monto a cobrar
- ✅ Estado del pedido

🚛 CONDUCTOR MEJORADO:
- ✅ Información de licencia y experiencia
- ✅ Calificación del conductor
- ✅ Estado actual y ubicación

🚗 VEHÍCULO MEJORADO:
- ✅ Capacidad de peso y volumen
- ✅ Tipo de combustible
- ✅ Características especiales (GPS, refrigeración)

⚙️ CONFIGURACIÓN DE ENTREGA:
- ✅ Opciones de optimización de ruta
- ✅ Configuración de notificaciones
- ✅ Restricciones operativas

📊 METADATOS:
- ✅ Información de trazabilidad
- ✅ Sistema de origen
- ✅ ID de correlación

🎯 BENEFICIOS:
- ✅ Mayor control sobre el proceso de entrega
- ✅ Mejor experiencia del cliente
- ✅ Optimización automática de rutas
- ✅ Trazabilidad completa
- ✅ Gestión de restricciones y horarios
  `);
}

// 🚀 Ejecutar la prueba
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  mostrarMejoras();
} else if (process.argv.includes('--mejoras')) {
  mostrarMejoras();
} else {
  testEndpointMejorado();
}
